{"name": "cloudbase-miniprogram-hearing-test", "version": "1.0.0", "description": "微信小程序听力测试项目 - 基于腾讯云开发", "main": "index.js", "scripts": {"generate-audio": "cd scripts && node generate-audio-files.js", "generate-audio-real": "cd scripts && node generate-audio-files.js --real-tts", "test-audio": "cd scripts && node test-audio-mapping.js", "verify-audio": "cd scripts && node verify-audio-format.js", "dev": "echo '请使用微信开发者工具打开项目进行开发'", "build": "echo '小程序项目无需构建，直接在开发者工具中预览或上传'"}, "keywords": ["miniprogram", "wechat", "cloudbase", "hearing-test", "tts", "audio"], "author": "CloudBase Team", "license": "MIT", "dependencies": {"sharp": "^0.34.2"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/cloudbase-miniprogram-hearing-test.git"}, "bugs": {"url": "https://github.com/your-username/cloudbase-miniprogram-hearing-test/issues"}, "homepage": "https://github.com/your-username/cloudbase-miniprogram-hearing-test#readme"}