/* 极简主义主题配置 */

/* 简洁专业配色方案 */
:root {
  /* 主色调 - 专业蓝色系 */
  --primary-blue: #3b82f6;
  --primary-blue-light: #93c5fd;
  --primary-blue-lighter: #dbeafe;
  --primary-blue-dark: #1d4ed8;

  /* 辅助色 - 浅色系 */
  --accent-green: #10b981;
  --accent-green-light: #a7f3d0;
  --accent-purple: #8b5cf6;
  --accent-purple-light: #c4b5fd;

  /* 浅色背景 */
  --soft-blue: #f0f9ff;
  --soft-green: #f0fdf4;
  --soft-purple: #faf5ff;
  --soft-gray: #f8fafc;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-card: #ffffff;
  --bg-accent: #f0f9ff;

  /* 文字颜色 */
  --text-primary: #374151;
  --text-secondary: #475569;
  --text-tertiary: #94a3b8;
  --text-white: #ffffff;

  /* 中性色 - 极简风格 */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e0;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  /* 阴影 */
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* 圆角 */
  --radius-sm: 8rpx;
  --radius-md: 16rpx;
  --radius-lg: 24rpx;
  --radius-xl: 32rpx;
  --radius-full: 50rpx;
}

/* 全局容器样式 - 极简风格 */
.app-container {
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
}

/* 极简卡片样式 */
.app-card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 48rpx;
  margin: 24rpx;
  border: 1px solid var(--gray-100);
}

.app-card-minimal {
  background: var(--bg-card);
  border-radius: var(--radius-md);
  padding: 32rpx;
  margin: 16rpx;
  border: 1px solid var(--gray-100);
  transition: all 0.2s ease;
}

.app-card-minimal:hover {
  border-color: var(--primary-teal-light);
  box-shadow: 0 4px 12px 0 rgba(20, 184, 166, 0.1);
}

/* 极简按钮样式 */
.app-btn-primary {
  background: var(--primary-gradient);
  color: var(--text-white);
  border: none;
  border-radius: var(--radius-lg);
  padding: 28rpx 56rpx;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px 0 rgba(20, 184, 166, 0.2);
  transition: all 0.2s ease;
}

.app-btn-primary:active {
  transform: translateY(1rpx);
  box-shadow: 0 1px 4px 0 rgba(20, 184, 166, 0.3);
}

.app-btn-secondary {
  background: transparent;
  color: var(--primary-teal);
  border: 2rpx solid var(--primary-teal-light);
  border-radius: var(--radius-lg);
  padding: 28rpx 56rpx;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.app-btn-secondary:active {
  background: var(--bg-soft);
  border-color: var(--primary-teal);
}

.app-btn-minimal {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: none;
  border-radius: var(--radius-md);
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  transition: all 0.2s ease;
}

.app-btn-minimal:active {
  background: var(--gray-200);
}

/* 进度条样式 */
.app-progress-bar {
  width: 100%;
  height: 12rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.app-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--white) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: var(--radius-sm);
  transition: width 0.3s ease;
}

/* 文字样式 */
.app-title {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--white);
  text-align: center;
  margin-bottom: 20rpx;
}

.app-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  margin-bottom: 40rpx;
}

.app-text-primary {
  color: var(--modern-primary);
}

.app-text-secondary {
  color: var(--gray-600);
}

.app-text-white {
  color: var(--white);
}

/* 图片选择样式 */
.app-image-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin: 40rpx 0;
}

.app-image-option {
  aspect-ratio: 1;
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 3rpx solid transparent;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
  position: relative;
}

.app-image-option.selected {
  border-color: var(--modern-primary);
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-xl);
}

.app-image-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(0, 242, 254, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.app-image-option.selected::before {
  opacity: 1;
}

.app-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 选中标记 */
.app-selected-mark {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 48rpx;
  height: 48rpx;
  background: var(--modern-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 24rpx;
  font-weight: bold;
  box-shadow: var(--shadow-md);
}

/* 底部功能区 - 极简风格 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-card);
  border-top: 1px solid var(--gray-100);
  padding: 20rpx 0 40rpx 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  backdrop-filter: blur(10px);
  z-index: 1000;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 24rpx;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  min-width: 120rpx;
}

.nav-item.active {
  background: var(--bg-soft);
}

.nav-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
  color: var(--text-tertiary);
  transition: color 0.2s ease;
}

.nav-item.active .nav-icon {
  color: var(--primary-teal);
}

.nav-label {
  font-size: 22rpx;
  color: var(--text-tertiary);
  font-weight: 400;
  transition: color 0.2s ease;
}

.nav-item.active .nav-label {
  color: var(--primary-teal);
  font-weight: 500;
}

/* 页面内容区域，为底部导航留出空间 */
.page-content {
  padding-bottom: 160rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .app-card {
    margin: 16rpx;
    padding: 32rpx;
  }

  .app-title {
    font-size: 40rpx;
  }

  .app-subtitle {
    font-size: 24rpx;
  }

  .bottom-nav {
    padding: 16rpx 0 32rpx 0;
  }

  .nav-item {
    min-width: 100rpx;
    padding: 12rpx 16rpx;
  }
}
