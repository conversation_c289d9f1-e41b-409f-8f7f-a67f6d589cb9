/**
 * 资源管理器 - 负责动态下载和缓存音频、图片资源
 */

class ResourceManager {
  constructor() {
    this.downloadQueue = [] // 下载队列
    this.downloading = false // 是否正在下载
    this.downloadProgress = 0 // 下载进度
    this.totalResources = 0 // 总资源数
    this.downloadedResources = 0 // 已下载资源数
    this.onProgressCallback = null // 进度回调
    this.onCompleteCallback = null // 完成回调
    this.silentMode = false // 静默模式（后台下载）
    this.progressTimer = null // 进度更新定时器
    this.successCount = 0 // 成功下载数量
    this.failCount = 0 // 失败下载数量
  }

  /**
   * 初始化资源管理器
   */
  async init(silentMode = false) {
    this.silentMode = silentMode
    console.log(`🚀 初始化资源管理器 (${silentMode ? '静默模式' : '普通模式'})`)

    try {
      // 检查本地缓存状态
      const cacheStatus = this.getCacheStatus()
      console.log('📦 缓存状态:', cacheStatus)

      if (!cacheStatus.isComplete) {
        console.log('📥 开始下载缺失资源...')
        await this.downloadMissingResources()
      } else {
        console.log('✅ 所有资源已缓存')
      }

      return true
    } catch (error) {
      console.error('❌ 资源管理器初始化失败:', error)
      if (!this.silentMode) {
        // 非静默模式才抛出错误
        throw error
      }
      return false
    }
  }

  /**
   * 检查缓存状态（验证实际文件存在）
   */
  getCacheStatus() {
    try {
      const audioCache = wx.getStorageSync('audio_cache_status') || {}
      const imageCache = wx.getStorageSync('image_cache_status') || {}

      // 不仅检查标记，还要验证实际文件是否存在
      const audioComplete = this.verifyCacheFiles('audio', audioCache)
      const imageComplete = this.verifyCacheFiles('image', imageCache)

      const result = {
        isComplete: audioComplete && imageComplete,
        audio: audioComplete,
        image: imageComplete,
        audioCount: audioCache.count || 0,
        imageCount: imageCache.count || 0
      }

      console.log('🔍 缓存状态验证结果:', result)
      return result
    } catch (error) {
      console.error('检查缓存状态失败:', error)
      return { isComplete: false, audio: false, image: false }
    }
  }

  /**
   * 验证缓存文件是否真实存在
   */
  verifyCacheFiles(type, cacheInfo) {
    if (!cacheInfo.isComplete) {
      return false
    }

    try {
      // 检查几个关键文件是否存在
      const testWords = ['书本', '冰箱', '回家'] // 测试几个文件
      let existCount = 0

      for (const word of testWords) {
        try {
          if (type === 'audio') {
            // 检查音频文件（女声和男声）
            const femalePath = this.getLocalResourcePath(word, 'audio', 'female')
            const malePath = this.getLocalResourcePath(word, 'audio', 'male')
            wx.getFileSystemManager().statSync(femalePath)
            wx.getFileSystemManager().statSync(malePath)
            existCount += 2
          } else if (type === 'image') {
            // 检查图片文件
            const imagePath = this.getLocalResourcePath(word, 'image')
            wx.getFileSystemManager().statSync(imagePath)
            existCount += 1
          }
        } catch (error) {
          console.log(`⚠️ 缓存文件不存在: ${word} (${type})`)
          // 如果任何一个测试文件不存在，认为缓存不完整
          this.clearCacheStatus(type) // 清理错误的缓存状态
          return false
        }
      }

      console.log(`✅ ${type} 缓存文件验证通过: ${existCount}个测试文件存在`)
      return true
    } catch (error) {
      console.error(`验证${type}缓存文件失败:`, error)
      this.clearCacheStatus(type)
      return false
    }
  }

  /**
   * 清理错误的缓存状态
   */
  clearCacheStatus(type) {
    try {
      if (type === 'audio') {
        wx.removeStorageSync('audio_cache_status')
        console.log('🗑️ 已清理音频缓存状态标记')
      } else if (type === 'image') {
        wx.removeStorageSync('image_cache_status')
        console.log('🗑️ 已清理图片缓存状态标记')
      }
    } catch (error) {
      console.error('清理缓存状态失败:', error)
    }
  }

  /**
   * 下载缺失的资源
   */
  async downloadMissingResources() {
    this.downloading = true
    this.downloadedResources = 0
    this.successCount = 0
    this.failCount = 0

    try {
      // 获取资源列表
      const resourceList = await this.getResourceList()
      console.log('🔍 云函数返回的资源列表:', resourceList)
      console.log('🔍 resourceList类型:', typeof resourceList)
      console.log('🔍 resourceList.audio:', resourceList.audio)
      console.log('🔍 resourceList.image:', resourceList.image)

      const { audio: audioList, image: imageList } = resourceList

      console.log('🔍 audioList类型:', typeof audioList, '是否为数组:', Array.isArray(audioList))
      console.log('🔍 imageList类型:', typeof imageList, '是否为数组:', Array.isArray(imageList))

      if (!Array.isArray(audioList) || !Array.isArray(imageList)) {
        throw new Error(`资源列表格式错误: audioList=${typeof audioList}, imageList=${typeof imageList}`)
      }

      this.totalResources = audioList.length + imageList.length
      console.log(`📊 总资源数: ${this.totalResources}`)

      // 检查哪些资源需要下载
      const missingAudio = this.getMissingResources(audioList, 'audio')
      const missingImages = this.getMissingResources(imageList, 'image')

      console.log(`📥 需要下载: 音频${missingAudio.length}个, 图片${missingImages.length}个`)

      // 启动定时进度更新
      this.startProgressTimer()

      let totalSuccess = 0
      let totalFail = 0

      // 下载音频资源
      if (missingAudio.length > 0) {
        const audioResult = await this.downloadResourceBatch(missingAudio, 'audio')
        totalSuccess += audioResult.successCount
        totalFail += audioResult.failCount
      }

      // 下载图片资源
      if (missingImages.length > 0) {
        const imageResult = await this.downloadResourceBatch(missingImages, 'image')
        totalSuccess += imageResult.successCount
        totalFail += imageResult.failCount
      }

      // 停止定时器
      this.stopProgressTimer()

      // 更新缓存状态（基于实际成功下载的数量）
      this.updateCacheStatus('audio', audioList.length)
      this.updateCacheStatus('image', imageList.length)

      if (totalFail > 0) {
        console.log(`⚠️ 资源下载完成: 成功${totalSuccess}个, 失败${totalFail}个`)
        if (this.onCompleteCallback) {
          this.onCompleteCallback(`部分资源下载失败 (${totalFail}个)`)
        }
      } else {
        console.log('✅ 所有资源下载完成')
        if (this.onCompleteCallback) {
          this.onCompleteCallback()
        }
      }

    } catch (error) {
      console.error('❌ 下载资源失败:', error)
      // 确保停止定时器
      this.stopProgressTimer()
      if (!this.silentMode) {
        // 非静默模式才抛出错误
        throw error
      }
    } finally {
      this.downloading = false
    }
  }

  /**
   * 获取资源列表
   */
  async getResourceList() {
    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: 'getResourceList',
        data: {},
        success: (res) => {
          if (res.result.success) {
            resolve(res.result.data)
          } else {
            reject(new Error(res.result.error))
          }
        },
        fail: reject
      })
    })
  }

  /**
   * 检查缺失的资源
   */
  getMissingResources(resourceList, type) {
    const missing = []
    
    resourceList.forEach(resource => {
      const localPath = this.getLocalPath(resource, type)
      
      try {
        const fileInfo = wx.getFileSystemManager().statSync(localPath)
        if (!fileInfo.isFile()) {
          missing.push(resource)
        }
      } catch (error) {
        // 文件不存在
        missing.push(resource)
      }
    })
    
    return missing
  }

  /**
   * 批量下载资源
   */
  async downloadResourceBatch(resources, type) {
    const batchSize = 3 // 并发下载数量
    let successCount = 0
    let failCount = 0

    console.log(`📥 开始批量下载 ${type} 资源: ${resources.length}个`)

    for (let i = 0; i < resources.length; i += batchSize) {
      const batch = resources.slice(i, i + batchSize)

      // 使用 Promise.allSettled 来处理部分失败的情况
      const results = await Promise.allSettled(
        batch.map(resource => this.downloadSingleResource(resource, type))
      )

      // 统计成功和失败的数量
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          successCount++
          this.successCount++ // 更新全局成功计数
          console.log(`✅ 下载成功: ${batch[index].word}`)
        } else {
          failCount++
          this.failCount++ // 更新全局失败计数

          const error = result.reason
          const word = batch[index].word

          // 区分文件不存在和其他错误
          if (error.message && error.message.includes('FILE_NOT_EXISTS')) {
            console.warn(`⚠️ 云存储文件不存在，跳过: ${word} (${batch[index].cloudPath})`)
          } else {
            console.warn(`⚠️ 下载失败但继续: ${word}`, error.message || error)
          }
        }
      })

      // 更新进度（基于处理的文件数，不管成功失败）
      this.downloadedResources += batch.length
      this.downloadProgress = (this.downloadedResources / this.totalResources) * 100

      console.log(`📊 下载进度: ${this.downloadProgress.toFixed(1)}% (成功:${successCount}, 失败:${failCount})`)

      if (this.onProgressCallback) {
        this.onProgressCallback(this.downloadProgress, this.downloadedResources, this.totalResources)
      }
    }

    console.log(`📋 批量下载完成: 成功${successCount}个, 失败${failCount}个`)

    // 如果失败太多，给出提示
    if (failCount > successCount) {
      console.warn(`⚠️ 大量文件下载失败 (${failCount}/${resources.length})，可能是云存储文件不存在`)
    }

    return { successCount, failCount }
  }

  /**
   * 下载单个资源
   */
  async downloadSingleResource(resource, type) {
    try {
      console.log(`🔍 开始下载: ${resource.word}, cloudPath: ${resource.cloudPath}, type: ${type}`)

      // 直接使用 wx.cloud.downloadFile 下载
      const localPath = await this.downloadFileDirectly(resource, type)

      console.log(`✅ 下载成功: ${resource.word} -> ${localPath}`)

      return localPath
    } catch (error) {
      console.error(`❌ 下载失败: ${resource.word}, cloudPath: ${resource.cloudPath}`, error)
      throw error
    }
  }

  /**
   * 直接使用 wx.cloud.downloadFile 下载文件到本地 (2025-06-30 重新实现)
   */
  async downloadFileDirectly(resource, type) {
    try {
      console.log(`📥 [NEW] 开始直接下载: ${resource.word}, fileID: ${resource.cloudPath}`)

      // 直接使用 wx.cloud.downloadFile，不再通过云函数
      const downloadResult = await new Promise((resolve, reject) => {
        wx.cloud.downloadFile({
          fileID: resource.cloudPath, // 使用完整的 fileID
          success: (res) => {
            console.log(`✅ [NEW] wx.cloud.downloadFile 成功: ${resource.word}`)
            console.log(`✅ [NEW] 临时文件路径: ${res.tempFilePath}`)

            // 直接使用临时文件路径
            // 在小程序中，临时文件在会话期间是有效的，无需复制
            resolve({
              tempFilePath: res.tempFilePath,
              savedFilePath: res.tempFilePath,
              word: resource.word,
              cloudPath: resource.cloudPath
            })
          },
          fail: (err) => {
            console.error(`❌ [NEW] wx.cloud.downloadFile 失败: ${resource.word}`, err)
            reject(new Error(`wx.cloud.downloadFile 失败: ${err.errMsg}`))
          }
        })
      })

      console.log(`✅ [NEW] 下载完成: ${resource.word}`)
      return downloadResult

    } catch (error) {
      console.error(`❌ [NEW] 下载失败: ${resource.word}`, error)
      throw error
    }
  }

  // 已删除 getTempFileURL 方法 - 现在统一使用 wx.cloud.downloadFile

  // 已删除 getTempFileURLDirect 方法 - 现在统一使用 wx.cloud.downloadFile

  // 已删除 downloadWithTempURL 方法 - 现在统一使用 wx.cloud.downloadFile

  /**
   * 将临时文件移动到目标位置
   */
  async moveFileToTarget(tempFilePath, targetPath) {
    return new Promise((resolve, reject) => {
      const fs = wx.getFileSystemManager()

      // 确保目标目录存在
      const targetDir = targetPath.substring(0, targetPath.lastIndexOf('/'))
      try {
        fs.mkdirSync(targetDir, true)
      } catch (error) {
        // 目录可能已存在，忽略错误
      }

      // 复制文件到目标位置
      fs.copyFile({
        srcPath: tempFilePath,
        destPath: targetPath,
        success: () => {
          console.log(`📁 文件移动成功: ${tempFilePath} -> ${targetPath}`)
          resolve()
        },
        fail: (error) => {
          console.error(`❌ 文件移动失败:`, error)
          reject(error)
        }
      })
    })
  }

  /**
   * 生成本地文件路径
   */
  getLocalPath(resource, type) {
    const fs = wx.getFileSystemManager()
    const userDataPath = wx.env.USER_DATA_PATH

    if (type === 'audio') {
      const gender = resource.gender === 'female' ? '女声' : '男声'
      const dir = `${userDataPath}/audio/${gender}`

      // 确保目录存在
      try {
        fs.mkdirSync(dir, true)
      } catch (error) {
        // 目录可能已存在
      }

      return `${dir}/${resource.word}.mp3`
    } else if (type === 'image') {
      const dir = `${userDataPath}/images/${resource.category}`

      // 确保目录存在
      try {
        fs.mkdirSync(dir, true)
      } catch (error) {
        // 目录可能已存在
      }

      return `${dir}/${resource.word}.png`
    } else {
      throw new Error(`未知的资源类型: ${type}`)
    }
  }

  // 已删除 downloadToLocal 方法 - 现在统一使用 downloadFileDirectly

  /**
   * 更新缓存状态
   */
  updateCacheStatus(type, count) {
    try {
      wx.setStorageSync(`${type}_cache_status`, {
        isComplete: true,
        count: count,
        updateTime: new Date().getTime()
      })
    } catch (error) {
      console.error(`更新${type}缓存状态失败:`, error)
    }
  }

  /**
   * 获取本地资源路径
   */
  getLocalResourcePath(word, type, gender = null) {
    if (type === 'audio') {
      const genderDir = gender === 'female' ? '女声' : '男声'
      return `${wx.env.USER_DATA_PATH}/audio/${genderDir}/${word}.mp3`
    } else if (type === 'image') {
      // 优先查找wordlist，如果不存在则查找words
      const wordlistPath = `${wx.env.USER_DATA_PATH}/images/wordlist/${word}.png`
      const wordsPath = `${wx.env.USER_DATA_PATH}/images/words/${word}.png`
      
      try {
        wx.getFileSystemManager().statSync(wordlistPath)
        return wordlistPath
      } catch (error) {
        return wordsPath
      }
    }
  }

  /**
   * 获取原始图片路径（用于降级）
   */
  getOriginalImagePath(word) {
    // 优先查找wordlist原始图片，如果不存在则查找words
    const wordlistPath = `/images/wordlist/${word}.png`
    const wordsPath = `/images/words/${word}.png`

    try {
      // 检查原始wordlist图片是否存在（这里只是路径检查，实际使用时小程序会处理）
      return wordlistPath
    } catch (error) {
      return wordsPath
    }
  }

  /**
   * 设置进度回调
   */
  setProgressCallback(callback) {
    this.onProgressCallback = callback
  }

  /**
   * 设置完成回调
   */
  setCompleteCallback(callback) {
    this.onCompleteCallback = callback
  }

  /**
   * 启动进度定时器
   */
  startProgressTimer() {
    if (this.progressTimer) {
      clearInterval(this.progressTimer)
    }

    this.progressTimer = setInterval(() => {
      if (this.downloading) {
        const progress = this.downloadProgress.toFixed(1)
        console.log(`⏰ 定时进度更新: ${progress}% (成功:${this.successCount}, 失败:${this.failCount}, 总计:${this.downloadedResources}/${this.totalResources})`)

        if (this.onProgressCallback) {
          this.onProgressCallback(this.downloadProgress, this.downloadedResources, this.totalResources)
        }
      }
    }, 5000) // 每5秒更新一次

    console.log('⏰ 已启动5秒定时进度更新')
  }

  /**
   * 停止进度定时器
   */
  stopProgressTimer() {
    if (this.progressTimer) {
      clearInterval(this.progressTimer)
      this.progressTimer = null
      console.log('⏰ 已停止定时进度更新')
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    try {
      wx.removeStorageSync('audio_cache_status')
      wx.removeStorageSync('image_cache_status')
      
      // 删除本地文件
      const fs = wx.getFileSystemManager()
      const userDataPath = wx.env.USER_DATA_PATH
      
      try {
        fs.rmdirSync(`${userDataPath}/audio`, true)
        fs.rmdirSync(`${userDataPath}/images`, true)
      } catch (error) {
        console.log('删除本地文件失败:', error)
      }
      
      console.log('✅ 缓存已清除')
    } catch (error) {
      console.error('❌ 清除缓存失败:', error)
    }
  }
}

// 创建全局实例
const resourceManager = new ResourceManager()

module.exports = resourceManager
