// 听力测试词汇映射配置
// 统一管理音频和图片文件，通过文件名直接匹配
// 音频文件存储在 miniprogram/audio/ 目录下，以85dB为基准
// 图片文件存储在 miniprogram/images/words/ 目录下

// 所有可用的词汇列表（音频和图片文件都通过文件名匹配）
const ALL_WORDS = [
  // 交通工具类
  '飞机', '火车', '汽车', '轮船', '公交', '地铁', '出租', '货车',

  // 水果类
  '苹果', '香蕉', '橙子', '葡萄', '草莓', '西瓜', '桃子', '梨子', '柠檬', '芒果',

  // 电器类
  '电脑', '手机', '电视', '冰箱', '洗衣', '空调', '烤箱', '音响', '相机',

  // 职业类
  '医生', '老师', '学生', '护士', '司机', '厨师', '警察', '消防', '邮递', '清洁',

  // 自然类
  '春天', '夏天', '秋天', '冬天', '阳光', '雨水', '雪花', '彩虹', '星星', '月亮'
];

// 词语分类映射，用于生成干扰项
const WORD_CATEGORIES = {
  '交通工具': ['飞机', '火车', '汽车', '轮船', '公交', '地铁', '出租', '货车'],
  '水果': ['苹果', '香蕉', '橙子', '葡萄', '草莓', '西瓜', '桃子', '梨子', '柠檬', '芒果'],
  '电器': ['电脑', '手机', '电视', '冰箱', '洗衣', '空调', '烤箱', '音响', '相机'],
  '职业': ['医生', '老师', '学生', '护士', '司机', '厨师', '警察', '消防', '邮递', '清洁'],
  '自然': ['春天', '夏天', '秋天', '冬天', '阳光', '雨水', '雪花', '彩虹', '星星', '月亮']
};

// 测试分贝级别配置（用于测试逻辑，不影响文件路径）
const TEST_LEVELS = {
  55: ALL_WORDS.slice(0, 16),  // 前16个词汇用于55dB测试
  70: ALL_WORDS.slice(16, 32), // 中间16个词汇用于70dB测试
  85: ALL_WORDS.slice(32, 47)  // 最后15个词汇用于85dB测试
};

// ===== 文件路径获取函数 =====

// 获取音频文件路径
function getAudioPath(word) {
  if (!ALL_WORDS.includes(word)) {
    return null;
  }
  return `/audio/${word}.mp3`;
}

// 获取图片文件路径
function getImagePath(word) {
  if (!ALL_WORDS.includes(word)) {
    return null;
  }
  return `/images/words/${word}.png`;
}

// ===== 词汇管理函数 =====

// 获取所有词汇列表
function getAllWords() {
  return ALL_WORDS.map(word => ({
    word: word,
    audioPath: getAudioPath(word),
    imagePath: getImagePath(word),
    soundLevel: 85 // 所有文件都以85dB为基准
  }));
}

// 获取指定分贝级别的测试词汇
function getWordsByLevel(soundLevel) {
  return TEST_LEVELS[soundLevel] || [];
}

// 随机选择指定数量的词汇
function getRandomWords(soundLevel, count = 2) {
  const words = getWordsByLevel(soundLevel);

  if (words.length <= count) {
    return words;
  }

  // 随机打乱数组
  const shuffled = words.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

// 获取词语所属的分类
function getWordCategory(word) {
  for (const category in WORD_CATEGORIES) {
    if (WORD_CATEGORIES[category].includes(word)) {
      return category;
    }
  }
  return null;
}

// ===== 测试选项生成函数 =====

// 生成4个选项（1个正确答案 + 3个干扰项）
function generateImageOptions(correctWord) {
  const options = [correctWord];
  const category = getWordCategory(correctWord);

  if (category) {
    // 优先从同类别中选择干扰项
    const categoryWords = WORD_CATEGORIES[category].filter(word => word !== correctWord);

    // 随机选择2个同类别的干扰项
    const shuffledCategoryWords = categoryWords.sort(() => Math.random() - 0.5);
    options.push(...shuffledCategoryWords.slice(0, 2));

    // 如果同类别词语不够，从其他类别补充
    if (options.length < 4) {
      const allWords = ALL_WORDS.filter(word => !options.includes(word));
      const shuffledAllWords = allWords.sort(() => Math.random() - 0.5);
      options.push(...shuffledAllWords.slice(0, 4 - options.length));
    }
  } else {
    // 如果找不到分类，随机选择3个其他词语
    const allWords = ALL_WORDS.filter(word => word !== correctWord);
    const shuffledWords = allWords.sort(() => Math.random() - 0.5);
    options.push(...shuffledWords.slice(0, 3));
  }

  // 打乱选项顺序
  const shuffledOptions = options.sort(() => Math.random() - 0.5);

  // 返回选项数组，包含图片路径和正确答案索引
  return {
    options: shuffledOptions.map(word => ({
      word: word,
      imagePath: getImagePath(word)
    })),
    correctIndex: shuffledOptions.indexOf(correctWord)
  };
}

// ===== 音频相关函数 =====

// 计算音量系数（以85dB为基准）
function getVolumeCoefficient(targetDB) {
  // 85dB为基准音量（系数1.0）
  if (targetDB >= 85) {
    return 1.0; // 85dB及以上保持原音量
  } else if (targetDB >= 70) {
    return 0.6; // 70dB约为85dB的60%音量
  } else if (targetDB >= 55) {
    return 0.3; // 55dB约为85dB的30%音量
  } else {
    return 0.1; // 更低分贝
  }
}

// ===== 文件检查函数 =====

// 检查词汇是否存在
function checkWordExists(word) {
  return ALL_WORDS.includes(word);
}

module.exports = {
  // 数据
  ALL_WORDS,
  WORD_CATEGORIES,
  TEST_LEVELS,
  
  // 路径获取
  getAudioPath,
  getImagePath,
  
  // 词汇管理
  getAllWords,
  getWordsByLevel,
  getRandomWords,
  getWordCategory,
  
  // 选项生成
  generateImageOptions,
  
  // 音频相关
  getVolumeCoefficient,
  
  // 检查函数
  checkWordExists
};
