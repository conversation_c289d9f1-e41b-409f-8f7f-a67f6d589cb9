/* pages/test-history/test-history.wxss */
.container {
  min-height: 100vh;
  background: #ffffff;
  padding: 30rpx;
}

/* 过滤器样式 */
.filter-section {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  border: 1px solid #e5e7eb;
}

.filter-label {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
  margin-right: 16rpx;
}

.filter-picker {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 1px solid #e5e7eb;
  min-width: 200rpx;
}

.filter-text {
  font-size: 28rpx;
  color: #374151;
  flex: 1;
}

.filter-arrow {
  font-size: 20rpx;
  color: #9ca3af;
  margin-left: 12rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #718096;
  margin-bottom: 60rpx;
}

.start-btn {
  background: linear-gradient(45deg, #4caf50, #45a049);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 30rpx 60rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

/* 历史记录列表 */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.history-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.test-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.test-date {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.test-type {
  font-size: 24rpx;
  color: #666;
}

.sai-score {
  display: flex;
  align-items: baseline;
  gap: 5rpx;
}

.score-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #6A5ACD;
}

.score-unit {
  font-size: 24rpx;
  color: #666;
}

.item-content {
  margin-bottom: 30rpx;
}

.level-badge {
  display: inline-block;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.level-badge.excellent {
  background: #e8f5e8;
  color: #4caf50;
}

.level-badge.good {
  background: #e3f2fd;
  color: #2196f3;
}

.level-badge.attention {
  background: #fff3e0;
  color: #ff9800;
}

.level-badge.professional {
  background: #ffebee;
  color: #f44336;
}

.test-summary {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.level-summary {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 10rpx;
  min-width: 100rpx;
}

.level-name {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.level-rate {
  font-size: 28rpx;
  font-weight: bold;
  color: #6A5ACD;
}

.item-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 15rpx;
  font-size: 26rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.view {
  background: #6A5ACD;
  color: white;
}

.action-btn.delete {
  background: #f5f5f5;
  color: #999;
}

.action-btn:active {
  transform: scale(0.95);
}

/* 底部操作 */
.bottom-actions {
  margin-top: 40rpx;
  padding-bottom: 40rpx;
}

.new-test-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(45deg, #6A5ACD, #9575cd);
  color: white;
  border: none;
  border-radius: 15rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

.new-test-btn:active {
  transform: scale(0.98);
}

/* 噪音测试等级样式 */
.noise-level {
  background: #fef3c7;
  color: #d97706;
}
