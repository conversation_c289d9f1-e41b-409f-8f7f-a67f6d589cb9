// pages/test-history/test-history.js
Page({
  data: {
    historyList: [],
    loading: true,
    totalCount: 0
  },

  onLoad: function(options) {
    this.loadTestHistory();
  },

  // 加载测试历史
  loadTestHistory: function() {
    wx.showLoading({ title: '加载中...' });
    
    wx.cloud.callFunction({
      name: 'getTestHistory',
      data: {}
    }).then(res => {
      wx.hideLoading();
      console.log('获取历史记录成功', res);
      
      if (res.result.success) {
        // 处理历史数据，格式化识别率
        const processedHistory = (res.result.data || []).map(item => {
          // 检查testResults是否存在
          if (!item.testResults || !Array.isArray(item.testResults)) {
            return {
              ...item,
              testResults: []
            };
          }

          // 处理测试结果中的识别率
          const processedTestResults = item.testResults.map(result => {
            const rate = typeof result.recognitionRate === 'number' ? result.recognitionRate : 0;
            return {
              ...result,
              recognitionRateFormatted: rate.toFixed(1)
            };
          });

          return {
            ...item,
            testResults: processedTestResults
          };
        });

        this.setData({
          historyList: processedHistory,
          totalCount: res.result.totalCount || processedHistory.length,
          loading: false
        });
      } else {
        this.setData({ loading: false });
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('获取历史记录失败', err);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    });
  },

  // 查看详细结果
  viewDetail: function(e) {
    const index = e.currentTarget.dataset.index;
    const testData = this.data.historyList[index];

    console.log('查看详情，测试数据:', testData);

    // 使用本地存储传递数据，避免URL长度限制
    try {
      // 传递原始的testResults数据（不是格式化后的）
      const originalTestResults = testData.testResults.map(result => ({
        level: result.level,
        recognitionRate: result.recognitionRate,
        correctCount: result.correctCount,
        totalCount: result.totalCount,
        results: result.results || []
      }));

      wx.setStorageSync('testResults', originalTestResults);
      console.log('历史数据已保存到本地存储');

      wx.navigateTo({
        url: '/pages/test-result/test-result'
      });
    } catch (e) {
      console.error('保存历史数据失败:', e);
      wx.showToast({
        title: '数据传递失败',
        icon: 'none'
      });
    }
  },

  // 删除记录
  deleteRecord: function(e) {
    const index = e.currentTarget.dataset.index;
    const record = this.data.historyList[index];
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条测试记录吗？',
      success: (res) => {
        if (res.confirm) {
          // 这里可以调用云函数删除记录
          const newList = [...this.data.historyList];
          newList.splice(index, 1);
          this.setData({ historyList: newList });
          
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }
      }
    });
  },

  // 开始新测试
  startNewTest: function() {
    wx.navigateTo({
      url: '/pages/hearing-test/hearing-test'
    });
  }
});
