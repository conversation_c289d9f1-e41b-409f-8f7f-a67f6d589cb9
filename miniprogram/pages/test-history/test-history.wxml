<!--pages/test-history/test-history.wxml-->
<view class="container">
  
  <!-- 空状态 -->
  <view wx:if="{{!loading && historyList.length === 0}}" class="empty-state">
    <text class="empty-icon">📊</text>
    <text class="empty-title">暂无测试记录</text>
    <text class="empty-desc">开始您的第一次听力测试吧</text>
    <button class="start-btn" bindtap="startNewTest">
      🎯 开始测试
    </button>
  </view>

  <!-- 历史记录列表 -->
  <view wx:if="{{historyList.length > 0}}" class="history-list">
    <view wx:for="{{historyList}}" wx:key="index" class="history-item">
      <view class="item-header">
        <view class="test-info">
          <text class="test-date">{{item.testDate}}</text>
          <text class="test-type">SAI听力测试</text>
        </view>
        <view class="sai-score">
          <text class="score-number">{{item.saiScore}}</text>
          <text class="score-unit">分</text>
        </view>
      </view>
      
      <view class="item-content">
        <view class="level-badge {{item.saiLevelClass}}">
          <text class="level-text">{{item.saiLevel}}</text>
        </view>
        
        <view class="test-summary">
          <view wx:for="{{item.testResults}}" wx:for-item="result" wx:key="index" class="level-summary">
            <text class="level-name">{{result.level}}dB</text>
            <text class="level-rate">{{result.recognitionRateFormatted}}%</text>
          </view>
        </view>
      </view>
      
      <view class="item-actions">
        <button class="action-btn view" bindtap="viewDetail" data-index="{{index}}">
          查看详情
        </button>
        <button class="action-btn delete" bindtap="deleteRecord" data-index="{{index}}">
          删除
        </button>
      </view>
    </view>
  </view>

  <!-- 底部操作 -->
  <view wx:if="{{historyList.length > 0}}" class="bottom-actions">
    <button class="new-test-btn" bindtap="startNewTest">
      ➕ 开始新测试
    </button>
  </view>

</view>
