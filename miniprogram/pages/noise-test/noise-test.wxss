/* pages/noise-test/noise-test.wxss */
.noise-test-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
}

/* 准备阶段样式 */
.ready-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.test-info {
  width: 100%;
  margin-bottom: 60rpx;
}

.info-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.info-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.info-content {
  display: flex;
  flex-direction: column;
}

.info-item {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  line-height: 1.5;
}

.noise-levels {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.levels-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.level-item {
  margin-bottom: 25rpx;
  padding-bottom: 25rpx;
  border-bottom: 1rpx solid #eee;
}

.level-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.level-info {
  display: flex;
  flex-direction: column;
}

.level-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.level-desc {
  font-size: 24rpx;
  color: #999;
}

.start-button {
  width: 400rpx;
  height: 80rpx;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 10rpx 30rpx rgba(238, 90, 36, 0.3);
}

/* 测试进行中样式 */
.testing-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-section {
  width: 100%;
  margin-bottom: 60rpx;
}

.progress-text {
  display: block;
  text-align: center;
  font-size: 28rpx;
  color: white;
  margin-bottom: 20rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00d2ff, #3a7bd5);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.current-test {
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.noise-info {
  text-align: center;
  margin-bottom: 40rpx;
}

.noise-level {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 10rpx;
}

.noise-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.audio-section {
  text-align: center;
  margin-bottom: 40rpx;
}

.play-button {
  width: 300rpx;
  height: 100rpx;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
}

.play-button.playing {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
}

.play-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.play-text {
  font-size: 28rpx;
  font-weight: bold;
}

.input-section {
  display: flex;
  flex-direction: column;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.response-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #ddd;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  margin-bottom: 30rpx;
  box-sizing: border-box;
}

.response-input:focus {
  border-color: #667eea;
}

.submit-button {
  width: 200rpx;
  height: 70rpx;
  background: linear-gradient(45deg, #00d2ff, #3a7bd5);
  color: white;
  border: none;
  border-radius: 35rpx;
  font-size: 28rpx;
  font-weight: bold;
  align-self: center;
}

.submit-button[disabled] {
  background: #ccc;
}

/* 完成阶段样式 */
.completed-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.result-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.result-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.result-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.result-summary {
  width: 100%;
  margin-bottom: 40rpx;
}

.summary-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.summary-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.summary-content {
  display: flex;
  flex-direction: column;
}

.accuracy-text {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 15rpx;
}

.total-text {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.detailed-results {
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.results-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.result-item {
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.result-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.result-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.result-level {
  font-size: 24rpx;
  color: #667eea;
  font-weight: bold;
  min-width: 80rpx;
}

.result-word {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  margin: 0 20rpx;
}

.result-status {
  font-size: 32rpx;
  font-weight: bold;
}

.result-status.correct {
  color: #27ae60;
}

.result-status.incorrect {
  color: #e74c3c;
}

.action-buttons {
  display: flex;
  gap: 30rpx;
}

.action-button {
  width: 160rpx;
  height: 70rpx;
  border: none;
  border-radius: 35rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.action-button.primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
}

.action-button.secondary {
  background: rgba(255, 255, 255, 0.8);
  color: #667eea;
  border: 2rpx solid #667eea;
}
