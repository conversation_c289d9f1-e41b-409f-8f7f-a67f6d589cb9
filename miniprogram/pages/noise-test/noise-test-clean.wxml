<!--pages/noise-test/noise-test.wxml-->
<view class="noise-test-container">
  
  <!-- 准备阶段 -->
  <view wx:if="{{testStatus === 'ready'}}" class="ready-section">
    <view class="header">
      <text class="title">🔊 噪声环境测听</text>
      <text class="subtitle">测试在不同噪声环境下的听力表现</text>
    </view>

    <view class="test-info">
      <view class="info-card">
        <text class="info-title">测试说明</text>
        <view class="info-content">
          <text class="info-item">• 将在4种不同噪声环境下进行测试</text>
          <text class="info-item">• 每个环境播放一个词汇</text>
          <text class="info-item">• 请输入您听到的词汇</text>
          <text class="info-item">• 建议佩戴耳机进行测试</text>
        </view>
      </view>

      <view class="noise-levels">
        <text class="levels-title">测试环境</text>
        <view wx:for="{{noiseLevels}}" wx:key="level" class="level-item">
          <view class="level-info">
            <text class="level-name">{{item.level}}dB - {{item.name}}</text>
            <text class="level-desc">{{item.description}}</text>
          </view>
        </view>
      </view>
    </view>

    <button class="start-button" bindtap="startNoiseTest">
      开始噪声测听
    </button>
  </view>

  <!-- 测试进行中 -->
  <view wx:if="{{testStatus === 'testing'}}" class="testing-section">
    <view class="progress-section">
      <text class="progress-text">进度: {{currentStepDisplay}}/{{totalSteps}}</text>
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{progressPercent}}%"></view>
      </view>
    </view>

    <view class="current-test">
      <view class="noise-info">
        <text class="noise-level">{{currentNoiseLevel}}dB</text>
        <text class="noise-desc">{{noiseLevels[currentStep].name}}</text>
      </view>

      <view class="audio-section">
        <button 
          class="play-button {{isPlaying ? 'playing' : ''}}" 
          bindtap="playTestAudio"
          disabled="{{isPlaying}}"
        >
          <text class="play-icon">{{isPlaying ? '🔊' : '▶️'}}</text>
          <text class="play-text">{{isPlaying ? '播放中...' : '播放测试音频'}}</text>
        </button>
      </view>

      <view class="input-section">
        <text class="input-label">请输入您听到的词汇：</text>
        <input 
          class="response-input" 
          placeholder="输入听到的词汇"
          value="{{userResponse}}"
          bindinput="onInputResponse"
        />
        <button 
          class="submit-button" 
          bindtap="submitCurrentTest"
          disabled="{{!canSubmit}}"
        >
          提交答案
        </button>
      </view>
    </view>
  </view>

  <!-- 测试完成 -->
  <view wx:if="{{testStatus === 'completed'}}" class="completed-section">
    <view class="result-header">
      <text class="result-title">🎯 噪声测听完成</text>
      <text class="result-subtitle">您的噪声环境听力表现</text>
    </view>

    <view class="result-summary">
      <view class="summary-card">
        <text class="summary-title">总体表现</text>
        <view class="summary-content">
          <text class="accuracy-text">准确率: {{accuracyRate}}%</text>
          <text class="total-text">完成测试: {{totalCount}}/{{totalSteps}}</text>
        </view>
      </view>
    </view>

    <view class="detailed-results">
      <text class="results-title">详细结果</text>
      <view wx:for="{{testResults}}" wx:key="index" class="result-item">
        <view class="result-info">
          <text class="result-level">{{item.noiseLevel}}dB</text>
          <text class="result-word">{{item.testWord}} → {{item.userResponse}}</text>
          <text class="result-status {{item.isCorrect ? 'correct' : 'incorrect'}}">
            {{item.isCorrect ? '✓' : '✗'}}
          </text>
        </view>
      </view>
    </view>

    <view class="action-buttons">
      <button class="action-button secondary" bindtap="retakeTest">
        重新测试
      </button>
      <button class="action-button primary" bindtap="goHome">
        返回首页
      </button>
    </view>
  </view>

</view>
