// pages/noise-test/noise-test.js
Page({
  data: {
    testStatus: 'ready', // ready, testing, completed
    currentNoiseLevel: 0,
    noiseLevels: [
      { level: 40, name: '安静环境', description: '图书馆、深夜住宅' },
      { level: 60, name: '正常对话', description: '办公室、餐厅' },
      { level: 80, name: '嘈杂环境', description: '繁忙街道、地铁' },
      { level: 100, name: '高噪声', description: '工厂、演唱会' }
    ],
    testResults: [],
    currentStep: 0,
    totalSteps: 4,
    isPlaying: false,
    currentWord: '',
    testWords: ['飞机', '火车', '电脑', '手机'],
    userResponse: '',
    showResult: false,
    accuracyRate: '0.0', // 格式化后的准确率
    correctCount: 0,
    totalCount: 0,
    progressPercent: 0, // 进度百分比
    currentStepDisplay: 1, // 当前步骤显示 (currentStep + 1)
    canSubmit: false // 是否可以提交答案
  },

  onLoad: function(options) {
    console.log('噪声测听页面加载');
  },

  // 开始噪声测听
  startNoiseTest: function() {
    this.setData({
      testStatus: 'testing',
      currentStep: 0,
      testResults: [],
      showResult: false,
      currentStepDisplay: 1,
      progressPercent: 0
    });
    
    this.startCurrentLevelTest();
  },

  // 开始当前级别测试
  startCurrentLevelTest: function() {
    const { currentStep, noiseLevels, testWords, totalSteps } = this.data;

    if (currentStep >= noiseLevels.length) {
      this.completeTest();
      return;
    }

    const currentLevel = noiseLevels[currentStep];
    const currentWord = testWords[currentStep];
    const progressPercent = ((currentStep + 1) / totalSteps) * 100;
    const currentStepDisplay = currentStep + 1;

    this.setData({
      currentNoiseLevel: currentLevel.level,
      currentWord: currentWord,
      userResponse: '',
      isPlaying: false,
      progressPercent: progressPercent,
      currentStepDisplay: currentStepDisplay,
      canSubmit: false
    });

    console.log(`开始 ${currentLevel.level}dB 噪声环境测试，测试词汇: ${currentWord}`);
  },

  // 播放测试音频
  playTestAudio: function() {
    const { currentWord, currentNoiseLevel } = this.data;
    
    this.setData({ isPlaying: true });
    
    // 模拟播放音频（在真实环境中会播放带噪声的音频）
    console.log(`播放测试音频: ${currentWord} (噪声级别: ${currentNoiseLevel}dB)`);
    
    // 模拟音频播放时间
    setTimeout(() => {
      this.setData({ isPlaying: false });
    }, 2000);
  },

  // 用户输入响应
  onInputResponse: function(e) {
    const value = e.detail.value;
    const canSubmit = value.trim().length > 0;

    this.setData({
      userResponse: value,
      canSubmit: canSubmit
    });
  },

  // 提交当前测试
  submitCurrentTest: function() {
    const { currentWord, userResponse, currentNoiseLevel, currentStep } = this.data;
    
    if (!userResponse.trim()) {
      wx.showToast({
        title: '请输入听到的词汇',
        icon: 'none'
      });
      return;
    }

    // 判断是否正确
    const isCorrect = userResponse.trim() === currentWord;
    
    // 记录测试结果
    const testResult = {
      noiseLevel: currentNoiseLevel,
      testWord: currentWord,
      userResponse: userResponse.trim(),
      isCorrect: isCorrect,
      timestamp: new Date().getTime()
    };

    const newResults = [...this.data.testResults, testResult];
    const nextStep = currentStep + 1;

    this.setData({
      testResults: newResults,
      currentStep: nextStep
    });

    // 短暂显示结果
    wx.showToast({
      title: isCorrect ? '正确！' : '错误',
      icon: isCorrect ? 'success' : 'none',
      duration: 1500
    });

    // 继续下一个测试
    setTimeout(() => {
      this.startCurrentLevelTest();
    }, 1500);
  },

  // 完成测试
  completeTest: function() {
    const { testResults } = this.data;

    // 计算总体准确率
    const correctCount = testResults.filter(result => result.isCorrect).length;
    const totalCount = testResults.length;
    const accuracy = totalCount > 0 ? (correctCount / totalCount * 100).toFixed(1) : '0.0';

    console.log('噪声测听完成', {
      totalTests: totalCount,
      correctCount: correctCount,
      accuracy: accuracy + '%'
    });

    this.setData({
      testStatus: 'completed',
      showResult: true,
      accuracyRate: accuracy,
      correctCount: correctCount,
      totalCount: totalCount
    });

    // 保存测试结果（可选）
    this.saveNoiseTestResult();
  },

  // 保存噪声测试结果
  saveNoiseTestResult: function() {
    const { testResults } = this.data;
    
    // 这里可以调用云函数保存结果
    console.log('保存噪声测试结果:', testResults);
  },

  // 重新测试
  retakeTest: function() {
    this.setData({
      testStatus: 'ready',
      currentStep: 0,
      testResults: [],
      showResult: false,
      userResponse: '',
      accuracyRate: '0.0',
      correctCount: 0,
      totalCount: 0,
      progressPercent: 0,
      currentStepDisplay: 1,
      canSubmit: false
    });
  },

  // 返回首页
  goHome: function() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 查看详细结果
  viewDetailResults: function() {
    // 可以跳转到详细结果页面或显示更多信息
    wx.showModal({
      title: '测试结果详情',
      content: this.formatDetailResults(),
      showCancel: false
    });
  },

  // 格式化详细结果
  formatDetailResults: function() {
    const { testResults } = this.data;
    let details = '';
    
    testResults.forEach((result, index) => {
      details += `${result.noiseLevel}dB: ${result.testWord} → ${result.userResponse} ${result.isCorrect ? '✓' : '✗'}\n`;
    });
    
    return details;
  }
});
