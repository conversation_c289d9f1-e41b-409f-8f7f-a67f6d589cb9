/* pages/hearing-test/hearing-test.wxss */
@import "../../styles/theme.wxss";

.container {
  min-height: 100vh;
  background: var(--bg-primary);
  padding: 40rpx 30rpx;
  box-sizing: border-box;
}

/* 准备阶段样式 */
.ready-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 环境监测样式 */
.environment-monitor {
  width: 100%;
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid var(--gray-100);
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.monitor-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.noise-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 15rpx;
  min-width: 120rpx;
}

.noise-indicator.safe {
  background: #e8f5e8;
  border: 2rpx solid #4caf50;
}

.noise-indicator.warning {
  background: #fff3e0;
  border: 2rpx solid #ff9800;
}

.noise-level {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.noise-status {
  font-size: 22rpx;
  margin-top: 5rpx;
}

.threshold-info {
  text-align: center;
  color: #666;
  font-size: 24rpx;
}

/* 测试说明样式 */
.instructions {
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
}

.instruction-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.instruction-list {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.instruction-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  background: #6A5ACD;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.step-text {
  flex: 1;
  font-size: 26rpx;
  color: #555;
  line-height: 1.5;
}

/* 开始按钮样式 */
.start-section {
  width: 100%;
  margin-top: 40rpx;
}

.start-btn {
  width: 100%;
  height: 100rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

.start-btn.enabled {
  background: linear-gradient(45deg, #4caf50, #45a049);
  color: white;
}

.start-btn.disabled {
  background: #ccc;
  color: #999;
}

/* 测试进行中样式 */
.testing-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
  min-height: calc(100vh - 200rpx);
  padding-bottom: 40rpx;
}

.progress-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin: 0 30rpx 20rpx 30rpx;
  width: calc(100% - 60rpx);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.current-level {
  font-size: 28rpx;
  font-weight: bold;
  color: #6A5ACD;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #e0e0e0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6A5ACD, #9575cd);
  transition: width 0.3s ease;
}

/* 词语显示样式 */
.word-display {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
  border-radius: 32rpx;
  padding: 50rpx 40rpx;
  margin: 0 30rpx 20rpx 30rpx;
  text-align: center;
  min-height: 180rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  width: calc(100% - 60rpx);
}

.playing-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.audio-wave {
  font-size: 80rpx;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.playing-text {
  font-size: 28rpx;
  color: #6A5ACD;
  font-weight: bold;
}

.playing-hint {
  font-size: 28rpx;
  color: #999;
  font-weight: normal;
  margin-top: 20rpx;
}

.response-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15rpx;
}

.response-prompt {
  font-size: 36rpx;
  color: #2c3e50;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.played-word {
  font-size: 24rpx;
  color: #666;
}

/* 响应模式样式 */
.response-mode {
  margin-bottom: 40rpx;
}

.mode-switch {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50rpx;
  padding: 8rpx;
  gap: 8rpx;
}

.mode-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  border: none;
  background: transparent;
  color: #666;
  transition: all 0.3s ease;
}

.mode-btn.active {
  background: #6A5ACD;
  color: white;
  box-shadow: 0 5rpx 15rpx rgba(106, 90, 205, 0.3);
}

/* 语音响应样式 */
.voice-response {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.record-btn {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  transition: all 0.3s ease;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

.record-btn:not(.recording) {
  background: linear-gradient(45deg, #6A5ACD, #9575cd);
}

.record-btn.recording {
  background: linear-gradient(45deg, #f44336, #d32f2f);
  animation: recordPulse 1s infinite;
}

.record-btn.processing {
  background: linear-gradient(45deg, #ffa500, #ff8c00);
  animation: processPulse 1.5s infinite;
}

.record-btn:disabled {
  opacity: 0.6;
  pointer-events: none;
}

@keyframes recordPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes processPulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.record-icon {
  font-size: 60rpx;
  color: white;
}

.record-text {
  font-size: 24rpx;
  color: white;
  font-weight: bold;
}

.response-preview {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15rpx;
  padding: 30rpx;
  text-align: center;
}

.response-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.response-text {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

/* 按键响应样式 */
.button-response {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
}

.word-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.word-option {
  height: 80rpx;
  border-radius: 15rpx;
  border: 2rpx solid #6A5ACD;
  background: white;
  color: #6A5ACD;
  font-size: 28rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.word-option:active {
  background: #6A5ACD;
  color: white;
  transform: scale(0.95);
}

/* 测试完成样式 */
.completed-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.sai-result {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  text-align: center;
  width: 100%;
}

.result-header {
  margin-bottom: 40rpx;
}

.result-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.result-subtitle {
  display: block;
  font-size: 26rpx;
  color: #666;
}

.sai-score {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 10rpx;
  margin-bottom: 30rpx;
}

.score-number {
  font-size: 120rpx;
  font-weight: bold;
  color: #6A5ACD;
}

.score-unit {
  font-size: 36rpx;
  color: #666;
}

.sai-level {
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  margin-bottom: 30rpx;
  display: inline-block;
}

.sai-level.excellent {
  background: #e8f5e8;
  color: #4caf50;
}

.sai-level.good {
  background: #e3f2fd;
  color: #2196f3;
}

.sai-level.attention {
  background: #fff3e0;
  color: #ff9800;
}

.sai-level.professional {
  background: #ffebee;
  color: #f44336;
}

.level-text {
  font-size: 32rpx;
  font-weight: bold;
}

.level-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 详细结果样式 */
.detailed-results {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  width: 100%;
}

.results-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.level-results {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.level-result {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
}

.level-info {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.level-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.level-rate {
  font-size: 36rpx;
  font-weight: bold;
  color: #6A5ACD;
}

.level-detail {
  text-align: right;
}

.correct-count {
  font-size: 24rpx;
  color: #666;
}

/* 操作按钮样式 */
.action-buttons {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-btn {
  height: 80rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(45deg, #6A5ACD, #9575cd);
  color: white;
}

.action-btn.secondary {
  background: white;
  color: #6A5ACD;
  border: 2rpx solid #6A5ACD;
}

.action-btn:not(.primary):not(.secondary) {
  background: #f5f5f5;
  color: #666;
}

.action-btn:active {
  transform: scale(0.98);
}

/* 图片选择响应区域样式 */
.image-response {
  width: calc(100% - 60rpx);
  padding: 0;
  margin: 30rpx 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.instruction-text {
  text-align: center;
  color: #333;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
  opacity: 0.8;
}

.image-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding: 0;
}

.image-option {
  border: 3rpx solid #e8ecf0;
  border-radius: 24rpx;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  aspect-ratio: 1;
  background: transparent;
}

.image-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.2) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-option:active {
  transform: scale(0.96);
}

.image-option.selected {
  border-color: #6A5ACD;
  box-shadow: 0 8rpx 32rpx rgba(106, 90, 205, 0.3);
  transform: translateY(-4rpx);
}

.image-option.selected::before {
  opacity: 1;
  background: linear-gradient(135deg, rgba(106, 90, 205, 0.15) 0%, rgba(106, 90, 205, 0.1) 100%);
}

.option-image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  object-fit: cover;
}

.image-option.selected .option-image {
  transform: scale(1.02);
  filter: brightness(1.1);
}

/* 选中标记样式 */
.selected-mark {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 60rpx;
  height: 60rpx;
  background: #6A5ACD;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(106, 90, 205, 0.4);
  animation: checkmarkPop 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.checkmark {
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1;
}

@keyframes checkmarkPop {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}



.confirm-section {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
  padding: 0;
}

.confirm-btn {
  background: linear-gradient(135deg, #6A5ACD 0%, #8B7ED8 50%, #9575cd 100%);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 28rpx 80rpx;
  font-size: 36rpx;
  font-weight: 700;
  letter-spacing: 2rpx;
  box-shadow: 0 8rpx 32rpx rgba(106, 90, 205, 0.4);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-width: 300rpx;
}

.confirm-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.confirm-btn:active {
  transform: scale(0.96) translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(106, 90, 205, 0.3);
}

.confirm-btn:active::before {
  left: 100%;
}

.confirm-btn.disabled {
  background: #ccc;
  color: #999;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.confirm-btn.disabled:active {
  transform: none;
  box-shadow: none;
}

.confirm-btn.disabled::before {
  display: none;
}
