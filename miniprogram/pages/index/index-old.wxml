<view class="page-container">
  <!-- 页面内容区域 -->
  <view class="page-content">
    <!-- 听音辩图 Tab 内容 -->
    <view class="content" wx:if="{{currentTab === 'sai'}}">
      <view class="header">
        <text class="title">🎧 听音辩图测试</text>
        <text class="subtitle">专业听力评估，通过听音识别图片</text>
      </view>
    <!-- 听音辩图 -->
    <view class="module-card action-card primary-action">
      <view class="card-header">
        <text class="card-icon">🎯</text>
        <text class="card-title">听音辩图</text>
      </view>
      <text class="card-desc">听音识别图片，三级声强测试</text>
      <button class="card-button primary" bindtap="startHearingTest">
        开始测试
      </button>
    </view>

    <!-- 历史记录 -->
    <view class="module-card action-card secondary-action">
      <view class="card-header">
        <text class="card-icon">📊</text>
        <text class="card-title">历史记录</text>
      </view>
      <text class="card-desc">查看历史测试记录和趋势分析</text>
      <button class="card-button secondary" bindtap="viewHistory">
        历史记录
      </button>
    </view>

    <!-- 噪音测试 -->
    <view class="module-card action-card noise-action">
      <view class="card-header">
        <text class="card-icon">🔊</text>
        <text class="card-title">噪音测试</text>
      </view>
      <text class="card-desc">测试在噪音环境下的听音辩图能力</text>
      <button class="card-button noise" bindtap="startNoiseTest">
        开始测试
      </button>
    </view>

    <!-- 测试说明 -->
    <view class="module-card test-info">
      <view class="card-header">
        <text class="card-icon">📋</text>
        <text class="card-title">测试说明</text>
      </view>
      <view class="info-content">
        <view class="key-features">
          <view class="feature-item">🔊 三级声强测试 (55/70/85dB)</view>
          <view class="feature-item">🎧 佩戴耳机，安静环境</view>
          <view class="feature-item">🖼️ 听音识别图片</view>
          <view class="feature-item">📊 专业听力评估报告</view>
        </view>
      </view>
    </view>

    <!-- 用户信息卡片 -->
    <view class="module-card info-card">
      <text class="label">您的OpenID：</text>
      <text class="value">{{openid || '获取中...'}}</text>
    </view>
    </view>

    <!-- 噪声测听 Tab 内容 -->
    <view class="noise-test-container" wx:if="{{currentTab === 'noise'}}">
      <view class="noise-header">
        <text class="noise-title">🔊 噪声环境测听</text>
        <text class="noise-subtitle">测试在不同噪声环境下的听力表现</text>
      </view>

      <view class="noise-features">
        <view class="feature-card">
          <view class="feature-icon">🎧</view>
          <text class="feature-title">多环境测试</text>
          <text class="feature-desc">40dB-100dB四种噪声环境</text>
        </view>
        <view class="feature-card">
          <view class="feature-icon">📊</view>
          <text class="feature-title">精准评估</text>
          <text class="feature-desc">准确评估噪声下听力表现</text>
        </view>
        <view class="feature-card">
          <view class="feature-icon">💡</view>
          <text class="feature-title">专业建议</text>
          <text class="feature-desc">提供个性化听力保护建议</text>
        </view>
      </view>

      <view class="noise-actions">
        <button class="noise-test-button" bindtap="startNoiseTest">
          开始噪声测听
        </button>
        <button class="noise-history-button" bindtap="viewHistory">
          查看历史记录
        </button>
      </view>
    </view>

    <!-- AI听健 Tab 内容 -->
    <view class="ai-chat-container" wx:if="{{currentTab === 'ai'}}">
      <agent-ui
        agentConfig="{{agentConfig}}"
        showBotAvatar="{{showBotAvatar}}"
        chatMode="{{chatMode}}"
        modelConfig="{{modelConfig}}"
        envShareConfig="{{envShareConfig}}">
      </agent-ui>
    </view>
  </view>

  <!-- 底部Tab导航 -->
  <view class="bottom-tab-bar">
    <view
      class="bottom-tab-item {{currentTab === 'sai' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="sai"
    >
      <view class="tab-icon">📊</view>
      <text class="tab-label">听音辩图</text>
    </view>
    <view
      class="bottom-tab-item {{currentTab === 'noise' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="noise"
    >
      <view class="tab-icon">🔊</view>
      <text class="tab-label">噪声测试</text>
    </view>
    <view
      class="bottom-tab-item {{currentTab === 'ai' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="ai"
    >
      <view class="tab-icon">🤖</view>
      <text class="tab-label">AI听健</text>
    </view>
  </view>
</view>