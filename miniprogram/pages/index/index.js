Page({
  data: {
    openid: '',
    currentTab: 'sai', // 默认显示SAI tab，可选值: sai, noise, ai

    // AI Agent 配置
    chatMode: "bot", // bot 表示使用agent
    showBotAvatar: true, // 是否在对话框左侧显示头像
    envShareConfig: {
      // 使用当前环境，不需要环境共享
    },
    agentConfig: {
      botId: "bot-f823faa5", // 听力健康AI助手的agent id
      allowWebSearch: true, // 允许联网搜索
      allowUploadFile: true, // 允许上传文件
      allowPullRefresh: true, // 允许下拉刷新
      allowUploadImage: true, // 允许上传图片
      showToolCallDetail: false, // 不展示toolCall细节，保持界面简洁
      allowMultiConversation: true, // 允许多会话
      allowVoice: true, // 允许语音输入
      showBotName: true // 显示机器人名称
    },
    modelConfig: {
      modelProvider: "deepseek", // 使用deepseek模型
      quickResponseModel: "deepseek-v3", // 快速响应模型
      logo: "", // 使用默认头像
      welcomeMsg: "你好！我是AI听健助手，专注于听力健康管理。我可以帮助您：\n\n🔍 解答听力相关问题\n📊 分析听力测试结果\n💡 提供听力保护建议\n🏥 推荐专业医疗资源\n\n有什么听力健康问题想要咨询吗？", // 欢迎语
    }
  },

  onLoad() {
    this.getOpenId();
  },

  // 获取用户OpenID
  getOpenId() {
    wx.cloud.callFunction({
      name: 'getOpenId',
      success: res => {
        console.log('获取OpenID成功', res);
        this.setData({
          openid: res.result.openid
        });
      },
      fail: err => {
        console.error('调用getOpenId云函数失败', err);
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      }
    });
  },

  // 开始听力测试
  startHearingTest() {
    wx.navigateTo({
      url: '/pages/hearing-test/hearing-test'
    });
  },

  // 查看历史记录
  viewHistory() {
    wx.navigateTo({
      url: '/pages/test-history/test-history'
    });
  },

  // 开始噪声测听
  startNoiseTest: function() {
    wx.navigateTo({
      url: '/pages/noise-test/noise-test'
    });
  },

  // 切换Tab
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });
  },


})