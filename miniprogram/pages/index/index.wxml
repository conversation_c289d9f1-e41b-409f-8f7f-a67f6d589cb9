<view class="page-container">
  <!-- 页面内容区域 -->
  <view class="page-content">
    <!-- SAI Tab 内容 -->
    <view class="content" wx:if="{{currentTab === 'sai'}}">
      <view class="header">
        <text class="title">🎧 听力社交够用指数测试</text>
        <text class="subtitle">SAI (Social Adequacy Index) 专业听力评估</text>
      </view>
    <!-- 开始听力测试 -->
    <view class="module-card action-card primary-action">
      <view class="card-header">
        <text class="card-icon">🎯</text>
        <text class="card-title">开始听力测试</text>
      </view>
      <text class="card-desc">专业SAI指数评估，三级声强测试</text>
      <button class="card-button primary" bindtap="startHearingTest">
        开始测试
      </button>
    </view>

    <!-- 历史记录 -->
    <view class="module-card action-card secondary-action">
      <view class="card-header">
        <text class="card-icon">📊</text>
        <text class="card-title">历史记录</text>
      </view>
      <text class="card-desc">查看历史测试记录和趋势分析</text>
      <button class="card-button secondary" bindtap="viewHistory">
        历史记录
      </button>
    </view>



    <!-- 功能特点 -->
    <view class="module-card features">
      <view class="card-header">
        <text class="card-icon">✨</text>
        <text class="card-title">测试特点</text>
      </view>
      <view class="feature-list">
        <view class="feature-item">🔊 三级声强测试 (55/70/85dB)</view>
        <view class="feature-item">🎤 智能语音识别</view>
        <view class="feature-item">📱 实时环境监测</view>
        <view class="feature-item">📈 专业SAI指数计算</view>
        <view class="feature-item">📋 详细测试报告</view>
      </view>
    </view>

    <!-- 使用说明 -->
    <view class="module-card instructions">
      <view class="card-header">
        <text class="card-icon">📋</text>
        <text class="card-title">使用说明</text>
      </view>
      <view class="instruction-list">
        <view class="instruction-item">
          <text class="step">1</text>
          <text class="step-text">请在安静环境中佩戴耳机进行测试</text>
        </view>
        <view class="instruction-item">
          <text class="step">2</text>
          <text class="step-text">系统将播放不同声强级别的词语</text>
        </view>
        <view class="instruction-item">
          <text class="step">3</text>
          <text class="step-text">可选择语音复述或按键选择方式响应</text>
        </view>
        <view class="instruction-item">
          <text class="step">4</text>
          <text class="step-text">测试完成后查看SAI指数和专业建议</text>
        </view>
      </view>
    </view>

    <!-- 用户信息卡片 -->
    <view class="module-card info-card">
      <text class="label">您的OpenID：</text>
      <text class="value">{{openid || '获取中...'}}</text>
    </view>
    </view>

    <!-- 噪声测听 Tab 内容 -->
    <view class="noise-test-container" wx:if="{{currentTab === 'noise'}}">
      <view class="noise-header">
        <text class="noise-title">🔊 噪声环境测听</text>
        <text class="noise-subtitle">测试在不同噪声环境下的听力表现</text>
      </view>

      <view class="noise-features">
        <view class="feature-card">
          <view class="feature-icon">🎧</view>
          <text class="feature-title">多环境测试</text>
          <text class="feature-desc">40dB-100dB四种噪声环境</text>
        </view>
        <view class="feature-card">
          <view class="feature-icon">📊</view>
          <text class="feature-title">精准评估</text>
          <text class="feature-desc">准确评估噪声下听力表现</text>
        </view>
        <view class="feature-card">
          <view class="feature-icon">💡</view>
          <text class="feature-title">专业建议</text>
          <text class="feature-desc">提供个性化听力保护建议</text>
        </view>
      </view>

      <view class="noise-actions">
        <button class="noise-test-button" bindtap="startNoiseTest">
          开始噪声测听
        </button>
        <button class="noise-history-button" bindtap="viewHistory">
          查看历史记录
        </button>
      </view>
    </view>

    <!-- AI听健 Tab 内容 -->
    <view class="ai-chat-container" wx:if="{{currentTab === 'ai'}}">
      <agent-ui
        agentConfig="{{agentConfig}}"
        showBotAvatar="{{showBotAvatar}}"
        chatMode="{{chatMode}}"
        modelConfig="{{modelConfig}}"
        envShareConfig="{{envShareConfig}}">
      </agent-ui>
    </view>
  </view>

  <!-- 底部Tab导航 -->
  <view class="bottom-tab-bar">
    <view
      class="bottom-tab-item {{currentTab === 'sai' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="sai"
    >
      <view class="tab-icon">📊</view>
      <text class="tab-label">SAI</text>
    </view>
    <view
      class="bottom-tab-item {{currentTab === 'noise' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="noise"
    >
      <view class="tab-icon">🔊</view>
      <text class="tab-label">噪声测试</text>
    </view>
    <view
      class="bottom-tab-item {{currentTab === 'ai' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="ai"
    >
      <view class="tab-icon">🤖</view>
      <text class="tab-label">AI听健</text>
    </view>
  </view>
</view>