/* 页面容器 */
.page-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
}

/* 页面内容区域 */
.page-content {
  flex: 1;
  padding: 40rpx 60rpx 120rpx 60rpx; /* 底部留出tab栏空间 */
  overflow-y: auto;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

/* 底部Tab栏样式 */
.bottom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  z-index: 9999;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.bottom-tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12rpx 8rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 0; /* 允许flex项目收缩 */
}

.bottom-tab-item:active {
  transform: scale(0.95);
}

.tab-icon {
  font-size: 38rpx;
  margin-bottom: 6rpx;
  transition: all 0.3s ease;
}

.tab-label {
  font-size: 22rpx;
  font-weight: 500;
  color: #999;
  transition: all 0.3s ease;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 激活状态 */
.bottom-tab-item.active .tab-icon {
  transform: scale(1.1);
}

.bottom-tab-item.active .tab-label {
  color: #6A5ACD;
  font-weight: bold;
}

.title {
  display: block;
  font-size: 56rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

.subtitle {
  display: block;
  font-size: 32rpx;
  color: rgba(255,255,255,0.8);
  font-weight: 300;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 45rpx;
  min-height: calc(100vh - 200rpx); /* 减去header和底部tab的高度 */
}

/* 统一的模块卡片样式 */
.module-card {
  width: 100%;
  background: rgba(255,255,255,0.95);
  padding: 40rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
  backdrop-filter: blur(10rpx);
  box-sizing: border-box;
}

/* 用户信息卡片 */
.info-card {
  /* 继承 module-card 样式 */
}

.label {
  display: block;
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.value {
  display: block;
  font-size: 28rpx;
  color: #333;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  word-break: break-all;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* 统一的卡片头部样式 */
.card-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 25rpx;
}

.card-icon {
  font-size: 48rpx;
  flex-shrink: 0;
}

.card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.card-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 50rpx;
  padding: 0 5rpx;
}

/* 功能卡片样式 */
.action-card {
  position: relative;
  transition: all 0.3s ease;
  padding: 45rpx 40rpx 40rpx 40rpx;
}

.action-card:active {
  transform: scale(0.98);
}

.primary-action {
  background: linear-gradient(135deg, #6A5ACD 0%, #9575cd 100%);
}

.primary-action .card-title {
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.1);
}

.primary-action .card-desc {
  color: rgba(255,255,255,0.9);
  text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.1);
}

.primary-action .card-icon {
  filter: brightness(0) invert(1);
}

.secondary-action {
  border: 2rpx solid #6A5ACD;
}



/* 卡片按钮样式 */
.card-button {
  width: 100%;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
  margin-top: 10rpx;
}

.card-button.primary {
  background: rgba(255,255,255,0.2);
  color: white;
  border: 2rpx solid rgba(255,255,255,0.3);
}

.card-button.secondary {
  background: #6A5ACD;
  color: white;
}



.card-button:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 功能特点列表 */
.feature-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.feature-item {
  font-size: 28rpx;
  color: #555;
  padding: 15rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #6A5ACD;
  line-height: 1.5;
}

/* 使用说明列表 */
.instruction-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.instruction-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #6A5ACD;
}

.step {
  width: 36rpx;
  height: 36rpx;
  background: #6A5ACD;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.step-text {
  flex: 1;
  font-size: 26rpx;
  color: #555;
  line-height: 1.5;
}



/* 响应式优化 */
@media (max-width: 750rpx) {
  .container {
    padding: 30rpx 40rpx;
  }

  .module-card {
    padding: 30rpx;
  }

  .card-icon {
    font-size: 40rpx;
  }

  .card-title {
    font-size: 32rpx;
  }
}

/* 动画效果 */
.module-card {
  animation: fadeInUp 0.6s ease-out;
}

.module-card:nth-child(1) { animation-delay: 0.1s; }
.module-card:nth-child(2) { animation-delay: 0.2s; }
.module-card:nth-child(3) { animation-delay: 0.3s; }
.module-card:nth-child(4) { animation-delay: 0.4s; }
.module-card:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片悬停效果 */
.action-card {
  position: relative;
  overflow: hidden;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.action-card:active::before {
  left: 100%;
}

/* AI听健 - 即将上线样式 */
.coming-soon {
  text-align: center;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  min-height: 400rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.coming-soon .card-title {
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

.coming-soon .card-icon {
  filter: brightness(0) invert(1);
}

.coming-soon-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  margin-top: 30rpx;
}

.coming-soon-text {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

.coming-soon-desc {
  font-size: 28rpx;
  color: rgba(255,255,255,0.9);
  text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.2);
  line-height: 1.5;
}

/* 噪声测听容器样式 */
.noise-test-container {
  flex: 1;
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.noise-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.noise-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.noise-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.noise-features {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin-bottom: 60rpx;
}

.feature-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 40rpx;
  margin-right: 25rpx;
}

.feature-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #666;
  flex: 1;
}

.noise-actions {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  align-items: center;
}

.noise-test-button {
  width: 400rpx;
  height: 80rpx;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 10rpx 30rpx rgba(238, 90, 36, 0.3);
}

.noise-history-button {
  width: 300rpx;
  height: 70rpx;
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  border-radius: 35rpx;
  font-size: 28rpx;
  font-weight: bold;
}

/* AI听健聊天容器样式 */
.ai-chat-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 120rpx; /* 为底部Tab栏留出空间 */
  background: #fff;
  z-index: 100;
}
