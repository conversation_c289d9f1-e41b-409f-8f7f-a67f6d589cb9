/* pages/test-result/test-result.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30rpx;
}

/* 报告头部 */
.report-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.report-title {
  display: block;
  font-size: 42rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 15rpx;
}

.test-date {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* SAI总分卡片 */
.sai-summary {
  margin-bottom: 40rpx;
}

.summary-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 50rpx 40rpx;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.score-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.score-label {
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

.score-display {
  display: flex;
  align-items: baseline;
  gap: 10rpx;
}

.score-number {
  font-size: 100rpx;
  font-weight: bold;
  color: #6A5ACD;
}

.score-unit {
  font-size: 32rpx;
  color: #666;
}

.level-badge {
  padding: 15rpx 30rpx;
  border-radius: 50rpx;
  font-weight: bold;
}

.level-badge.excellent {
  background: #e8f5e8;
  color: #4caf50;
}

.level-badge.good {
  background: #e3f2fd;
  color: #2196f3;
}

.level-badge.attention {
  background: #fff3e0;
  color: #ff9800;
}

.level-badge.professional {
  background: #ffebee;
  color: #f44336;
}

.level-text {
  font-size: 28rpx;
}

/* 详细分析 */
.detailed-analysis {
  margin-bottom: 40rpx;
}

.analysis-title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 30rpx;
  text-align: center;
}

.level-cards {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.level-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: block;
  margin-bottom: 20rpx;
}

.level-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.level-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.rate-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #6A5ACD;
}

.level-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.5;
  display: block;
  width: 100%;
}

.recognition-rate {
  font-size: 32rpx;
  font-weight: bold;
  color: #6A5ACD;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: #e0e0e0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6A5ACD, #9575cd);
  transition: width 0.5s ease;
}

.card-details {
  margin-bottom: 25rpx;
}

.correct-info {
  font-size: 24rpx;
  color: #666;
}

/* 词语详情 */
.word-details {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.word-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-radius: 10rpx;
  border-left: 6rpx solid;
}

.word-item.correct {
  background: #f1f8e9;
  border-left-color: #4caf50;
}

.word-item.incorrect {
  background: #ffebee;
  border-left-color: #f44336;
}

.word-info {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.word-text {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.response-text {
  font-size: 22rpx;
  color: #666;
}

.result-icon {
  font-size: 32rpx;
}

/* 图表样式 */
.chart-section {
  margin-bottom: 40rpx;
}

.chart-title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 30rpx;
  text-align: center;
}

.chart-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15rpx;
  padding: 30rpx;
}

.chart-grid {
  display: flex;
  height: 300rpx;
}

.y-axis {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 60rpx;
  padding-right: 20rpx;
}

.y-label {
  font-size: 20rpx;
  color: #666;
  text-align: right;
}

.chart-area {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  border-left: 2rpx solid #e0e0e0;
  border-bottom: 2rpx solid #e0e0e0;
  padding: 20rpx 10rpx 40rpx 20rpx;
  position: relative;
}

.chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80rpx;
}

.bar-fill {
  width: 60rpx;
  background: linear-gradient(180deg, #6A5ACD, #9575cd);
  border-radius: 4rpx 4rpx 0 0;
  margin-bottom: 15rpx;
  transition: height 0.8s ease;
  min-height: 10rpx;
}

.bar-label {
  font-size: 20rpx;
  color: #666;
  text-align: center;
  transform: rotate(-45deg);
  white-space: nowrap;
}

/* 专业建议 */
.recommendations {
  margin-bottom: 40rpx;
}

.rec-title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 30rpx;
  text-align: center;
}

.rec-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15rpx;
  padding: 30rpx;
}

.rec-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  margin-bottom: 25rpx;
}

.rec-item:last-child {
  margin-bottom: 0;
}

.rec-icon {
  font-size: 32rpx;
  flex-shrink: 0;
}

.rec-text {
  flex: 1;
  font-size: 26rpx;
  color: #555;
  line-height: 1.6;
}

/* 操作按钮 */
.action-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-btn {
  height: 80rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(45deg, #6A5ACD, #9575cd);
  color: white;
}

.action-btn.secondary {
  background: white;
  color: #6A5ACD;
  border: 2rpx solid #6A5ACD;
}

.action-btn:not(.primary):not(.secondary) {
  background: rgba(255, 255, 255, 0.9);
  color: #666;
}

.action-btn:active {
  transform: scale(0.98);
}
