<!--pages/test-result/test-result.wxml-->
<view class="container">
  
  <!-- 报告头部 -->
  <view class="report-header">
    <text class="report-title">📊 听力测试报告</text>
    <text class="test-date">测试时间: {{testDate}}</text>
  </view>

  <!-- SAI总分 -->
  <view class="sai-summary">
    <view class="summary-card">
      <view class="score-section">
        <text class="score-label">社交够用指数 (SAI)</text>
        <view class="score-display">
          <text class="score-number">{{saiScore}}</text>
          <text class="score-unit">分</text>
        </view>
        <view class="level-badge {{saiLevelClass}}">
          <text class="level-text">{{saiLevel}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 各级别详细结果 -->
  <view class="detailed-analysis">
    <view class="analysis-title">🔍 详细分析</view>

    <!-- 调试信息 -->
    <view wx:if="{{testResults.length === 0}}" class="debug-info">
      <text style="color: red;">⚠️ 没有测试结果数据</text>
    </view>

    <view class="level-cards">
      <view wx:for="{{testResults}}" wx:key="index" class="level-card">
        <view class="card-header">
          <view class="level-info">
            <text class="level-text">{{item.level}}dB 测试</text>
            <text class="rate-text">准确率: {{item.recognitionRate}}%</text>
          </view>
        </view>

        <view class="progress-bar">
          <view class="progress-fill" style="width: {{item.recognitionRateNum || 0}}%"></view>
        </view>

        <view class="card-details">
          <text class="correct-info">正确识别: {{item.correctCount || 0}}/{{item.totalCount || 0}} 个词语</text>
        </view>
        
        <!-- 词语详情 -->
        <view class="word-details">
          <view wx:for="{{item.results}}" wx:for-item="wordResult" wx:key="index" 
                class="word-item {{wordResult.isCorrect ? 'correct' : 'incorrect'}}">
            <view class="word-info">
              <text class="word-text">{{wordResult.word}}</text>
              <text class="response-text">→ {{wordResult.response || '无响应'}}</text>
            </view>
            <view class="result-icon">
              {{wordResult.isCorrect ? '✅' : '❌'}}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 听力曲线图 -->
  <view class="chart-section">
    <view class="chart-title">📈 听力识别曲线</view>
    <view class="chart-container">
      <view class="chart-grid">
        <!-- Y轴标签 -->
        <view class="y-axis">
          <text class="y-label">100%</text>
          <text class="y-label">75%</text>
          <text class="y-label">50%</text>
          <text class="y-label">25%</text>
          <text class="y-label">0%</text>
        </view>
        
        <!-- 图表区域 -->
        <view class="chart-area">
          <view wx:for="{{chartData}}" wx:key="index" class="chart-bar">
            <view class="bar-fill" style="height: {{item.rate}}%"></view>
            <text class="bar-label">{{item.level}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 专业建议 -->
  <view class="recommendations">
    <view class="rec-title">💡 专业建议</view>
    <view class="rec-content">
      <view wx:if="{{saiLevel === '优秀'}}" class="rec-item">
        <text class="rec-icon">🎉</text>
        <text class="rec-text">您的听力状况非常好！建议继续保持良好的听力保护习惯，定期进行听力检查。</text>
      </view>
      
      <view wx:if="{{saiLevel === '良好'}}" class="rec-item">
        <text class="rec-icon">👍</text>
        <text class="rec-text">您的听力状况良好。在嘈杂环境中建议使用降噪耳机，避免长时间暴露在高分贝环境中。</text>
      </view>
      
      <view wx:if="{{saiLevel === '需关注'}}" class="rec-item">
        <text class="rec-icon">⚠️</text>
        <text class="rec-text">建议关注听力健康，减少噪音暴露，考虑咨询听力专家进行进一步评估。</text>
      </view>
      
      <view wx:if="{{saiLevel === '建议专业检测'}}" class="rec-item">
        <text class="rec-icon">🏥</text>
        <text class="rec-text">强烈建议尽快咨询专业听力师或耳鼻喉科医生，进行详细的听力检测和评估。</text>
      </view>
      
      <!-- 通用建议 -->
      <view class="rec-item">
        <text class="rec-icon">📱</text>
        <text class="rec-text">建议定期使用本应用进行听力自测，监测听力变化趋势。</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button class="action-btn primary" bindtap="shareResult">
      📤 分享报告
    </button>
    <button class="action-btn secondary" bindtap="saveReport">
      💾 保存报告
    </button>
    <button class="action-btn" bindtap="viewHistory">
      📋 历史记录
    </button>
    <button class="action-btn" bindtap="retryTest">
      🔄 重新测试
    </button>
    <button class="action-btn" bindtap="goHome">
      🏠 返回首页
    </button>
  </view>

</view>
