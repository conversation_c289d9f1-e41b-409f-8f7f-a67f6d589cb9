// pages/test-result/test-result.js
Page({
  data: {
    testResults: [],
    saiScore: 0,
    saiLevel: '',
    saiLevelClass: '',
    testDate: '',
    chartData: []
  },

  onLoad: function(options) {
    // 优先从本地存储读取数据
    let results = null;
    
    try {
      results = wx.getStorageSync('testResults');
      if (results) {
        console.log('从本地存储读取测试结果:', results);
        // 清除本地存储的数据
        wx.removeStorageSync('testResults');
      }
    } catch (e) {
      console.log('从本地存储读取失败，尝试从URL参数读取');
    }
    
    // 如果本地存储没有数据，尝试从URL参数读取（向后兼容）
    if (!results && options.results) {
      try {
        results = JSON.parse(options.results);
        console.log('从URL参数读取测试结果:', results);
      } catch (e) {
        console.error('解析URL参数失败', e);
      }
    }
    
    if (results && results.length > 0) {
      // 确保每个结果都有正确的数据格式
      const processedResults = results.map(result => {
        const rate = typeof result.recognitionRate === 'number' ? result.recognitionRate : 0;
        return {
          level: result.level || 0,
          recognitionRate: rate.toFixed(1),
          recognitionRateNum: rate,
          correctCount: result.correctCount || 0,
          totalCount: result.totalCount || 0,
          results: result.results || []
        };
      });

      console.log('处理后的测试结果:', processedResults);
      
      // 详细检查每个结果的数据
      processedResults.forEach((result, index) => {
        console.log(`结果 ${index + 1}:`, {
          level: result.level,
          recognitionRate: result.recognitionRate,
          recognitionRateNum: result.recognitionRateNum,
          correctCount: result.correctCount,
          totalCount: result.totalCount
        });
      });
      
      this.setData({ testResults: processedResults });
      this.calculateSAI();
      this.prepareChartData();
    } else {
      console.error('❌ 没有找到测试结果数据');
      wx.showToast({
        title: '没有测试数据',
        icon: 'none'
      });
    }
    
    this.setData({
      testDate: new Date().toLocaleString('zh-CN')
    });
  },

  // 计算SAI指数
  calculateSAI: function() {
    const { testResults } = this.data;

    console.log('计算SAI指数，测试结果:', testResults);

    if (testResults.length === 0) {
      console.log('没有测试结果数据');
      return;
    }

    // 详细显示每个级别的识别率
    testResults.forEach((result, index) => {
      console.log(`级别 ${result.level}dB: 识别率 ${result.recognitionRate}%`);
    });

    const totalRate = testResults.reduce((sum, result) => sum + (result.recognitionRateNum || 0), 0);
    const saiScore = totalRate / testResults.length;

    console.log('总识别率:', totalRate, '平均SAI分数:', saiScore);

    let saiLevel = '';
    let saiLevelClass = '';
    if (saiScore >= 90) {
      saiLevel = '优秀';
      saiLevelClass = 'excellent';
    } else if (saiScore >= 75) {
      saiLevel = '良好';
      saiLevelClass = 'good';
    } else if (saiScore >= 60) {
      saiLevel = '需关注';
      saiLevelClass = 'attention';
    } else {
      saiLevel = '建议专业检测';
      saiLevelClass = 'professional';
    }

    this.setData({
      saiScore: saiScore.toFixed(1),
      saiLevel: saiLevel,
      saiLevelClass: saiLevelClass
    });
  },

  // 准备图表数据
  prepareChartData: function() {
    const { testResults } = this.data;
    const chartData = testResults.map(result => ({
      level: result.level + 'dB',
      rate: result.recognitionRateNum || 0
    }));
    
    console.log('图表数据:', chartData);
    this.setData({ chartData });
  },

  // 分享测试结果
  shareResult: function() {
    wx.showShareMenu({
      withShareTicket: true
    });
  },

  // 重新测试
  retakeTest: function() {
    wx.navigateBack();
  },

  // 返回首页
  goHome: function() {
    wx.reLaunch({
      url: '/pages/index/index'
    });
  }
});
