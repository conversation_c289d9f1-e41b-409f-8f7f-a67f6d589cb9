// app.js
const resourceManager = require('./utils/resourceManager');

App({
  onLaunch: function() {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    } else {
      wx.cloud.init({
        env: 'cloud1-0gjev5gfdef4d262', // 云开发环境 ID
        traceUser: true,
      });
    }

    // 后台初始化资源管理器（静默下载）
    this.initResourcesInBackground();

    // 获取用户信息
    wx.getSetting({
      success: res => {
        if (res.authSetting['scope.userInfo']) {
          // 已经授权，可以直接调用 getUserInfo 获取头像昵称
          wx.getUserInfo({
            success: res => {
              this.globalData.userInfo = res.userInfo;
              // 由于 getUserInfo 是网络请求，可能会在 Page.onLoad 之后才返回
              // 所以此处加入 callback 以防止这种情况
              if (this.userInfoReadyCallback) {
                this.userInfoReadyCallback(res);
              }
            }
          });
        }
      }
    });
  },

  /**
   * 后台初始化资源（静默下载）
   */
  async initResourcesInBackground() {
    try {
      console.log('🚀 开始后台资源初始化...');

      // 检查缓存状态
      const cacheStatus = resourceManager.getCacheStatus();
      console.log('📦 缓存状态:', cacheStatus);

      if (cacheStatus.isComplete) {
        console.log('✅ 资源已缓存，无需下载');
        return;
      }

      console.log('🔍 缓存不完整，准备开始下载...');

      console.log('📥 开始后台下载资源...');

      // 设置静默下载回调
      resourceManager.setProgressCallback((progress, downloaded, total) => {
        console.log(`📊 后台下载进度: ${progress.toFixed(1)}% (${downloaded}/${total})`);

        // 可以在这里添加更多的进度显示逻辑
        // 比如更新全局状态、显示通知等
      });

      resourceManager.setCompleteCallback((message) => {
        if (message) {
          console.log('⚠️ 后台资源下载完成（部分失败）:', message);
          wx.showToast({
            title: '资源基本准备完成',
            icon: 'none',
            duration: 3000
          });
        } else {
          console.log('✅ 后台资源下载完成');
          wx.showToast({
            title: '资源准备完成',
            icon: 'success',
            duration: 2000
          });
        }
      });

      // 开始后台下载（静默模式）
      console.log('🚀 调用resourceManager.init()...');
      await resourceManager.init(true);
      console.log('✅ resourceManager.init()完成');

    } catch (error) {
      console.error('❌ 后台资源初始化失败:', error);
      console.error('错误详情:', error.message, error.stack);
      // 静默失败，不影响用户使用
    }
  },

  globalData: {
  }
});