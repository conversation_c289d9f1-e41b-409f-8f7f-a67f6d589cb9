以下是根据您的要求整理的微信小程序开发需求文档，清晰划分了核心功能模块和技术实现要点：

### 一、项目概述
**项目名称**：听力健康小程序  
**目标用户**：普通用户（家庭/社区场景）、听力师验配师  
**核心功能**：通过标准化听力测试计算社交够用指数（SAI），提供专业级听力评估；提供听力大模型对话诊断能力。

---

### 二、核心功能需求

#### 1. 听音辩图模块
- **声强分级测试**：
    - 在55dB、70dB、85dB三个固定声强级播放测试语音
    - 每个声强级从语音样本库中随机播放2组标准双音节词（如“飞机”、“火车”，“苹果”等）
- **语音样本库**：
    - 建立标准化中文双音节词库（30组）
    - 语音的原始分贝为85dB,播放不同声强级时通过乘以音量系数控制
- **图片样本库**：
    - 建立标准化图片库（30组），与语言样本通过文件名一一对应

#### 2. 环境监测系统
- **实时噪音检测**：
    - 调用手机麦克风实时监测环境噪音值
    - 动态显示当前环境分贝值（dB）,每秒刷新一次
- **测试条件控制**：
    - 噪音阈值设定（建议<30dB）
    - 环境超标时弹窗提示，可能影响测试结果
    - 提供“静音环境检测”引导功能

#### 3. 听力测试交互说明
  ```mermaid
  graph TD
    A[语音播放] --> B[显示4个图片选项]
    B --> C[选择图片]
    C --> D[保存图片选择以及对错]
    D --> A[播放下一个词语]
  直到播放完毕所有测试语言
  ```

#### 4. SAI计算引擎
- **数据计算模型**：
  ```
  言语识别率 = 正确识别词语数 / 总测试词语数
  SAI指数 = (55dB识别率 + 70dB识别率 + 85dB识别率) / 3
  ```
- **智能分析**：
    - 生成听力曲线可视化图表
    - 根据SAI值提供分级建议（优秀/良好/需关注/建议专业检测）

---

### 三、技术实现需求

#### 1. 音频处理系统
- **精准声强控制**：
    - 实现±3dB的播放精度控制
    - 耳机类型自动校准（蓝牙/有线）
- **降噪处理**：
    - 集成Web Audio API进行实时音频处理
    - 背景噪音过滤算法


#### 2. 硬件适配要求
- **移动设备兼容**：
    - 支持iOS/Android主流机型
    - 自适应不同麦克风灵敏度
- **外设扩展**：
    - 蓝牙听力设备对接接口
    - 预留助听器数据互通能力

---

### 四、数据管理需求

#### 1. 测试数据记录
- 存储每次测试的：
    - 环境噪音曲线
    - 各声强级识别率
    - 用户响应原始数据
    - SAI计算结果

#### 2. 用户档案系统
- 微信授权建立个人档案
- 历史测试数据对比功能
- 生成可分享的测试报告（PDF格式）
---

### 五、无障碍设计
- 大字体/高对比度界面
- 震动触觉反馈
- 语音引导全程辅助

---
