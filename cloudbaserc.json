{"envId": "cloud1-0gjev5gfdef4d262", "functionRoot": "cloudfunctions", "functions": [{"name": "generateTTS", "timeout": 60, "runtime": "Nodejs18.15"}, {"name": "speechRecognition", "timeout": 60, "runtime": "Nodejs18.15"}, {"name": "saveTestResult", "timeout": 30, "runtime": "Nodejs18.15"}, {"name": "getTestHistory", "timeout": 30, "runtime": "Nodejs18.15"}, {"name": "generateReport", "timeout": 60, "runtime": "Nodejs18.15"}, {"name": "getOpenId", "timeout": 30, "runtime": "Nodejs18.15"}, {"name": "getResourceList", "timeout": 30, "runtime": "Nodejs18.15"}, {"name": "downloadResource", "timeout": 30, "runtime": "Nodejs18.15"}]}