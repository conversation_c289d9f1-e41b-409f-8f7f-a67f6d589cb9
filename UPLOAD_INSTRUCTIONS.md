
# 资源上传使用说明

## 前置条件
1. 安装 @cloudbase/cli: `npm install -g @cloudbase/cli`
2. 登录云开发: `tcb login`
3. 确保在正确的项目目录下

## 上传步骤
1. 运行资源上传脚本: `node scripts/upload-resources.js`
2. 执行生成的批量上传脚本: `./upload-resources.sh`
3. 等待所有资源上传完成

## 验证上传
1. 登录云开发控制台
2. 进入云存储管理
3. 检查 resources/ 目录下的文件

## 目录结构
```
resources/
├── audio/
│   ├── female/          # 女声音频
│   │   ├── 书本.mp3
│   │   ├── 冰箱.mp3
│   │   └── ...
│   └── male/            # 男声音频
│       ├── 书本.mp3
│       ├── 冰箱.mp3
│       └── ...
└── images/
    ├── wordlist/        # 词汇图片
    │   ├── 书本.png
    │   ├── 冰箱.png
    │   └── ...
    └── words/           # 其他图片
        ├── 公交.png
        ├── 冬天.png
        └── ...
```

## 注意事项
- 上传过程可能需要较长时间，请耐心等待
- 如果上传失败，可以重新运行脚本
- 确保网络连接稳定
