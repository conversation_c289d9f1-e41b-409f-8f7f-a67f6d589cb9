// 测试文件保存修复
console.log('🧪 测试文件保存修复')

console.log('\n请在微信开发者工具控制台中执行以下测试代码:')

const testCode = `
console.log('🔍 测试文件保存修复...');

// 测试 wx.cloud.downloadFile 的完整流程
const testResource = {
  word: '测试文件',
  cloudPath: 'cloud://cloud1-0gjev5gfdef4d262.636c-cloud1-0gjev5gfdef4d262-1314700961/resources/images/wordlist/冰箱.png'
};

console.log('\\n📋 测试完整的下载和保存流程');

wx.cloud.downloadFile({
  fileID: testResource.cloudPath,
  success: (res) => {
    console.log('✅ wx.cloud.downloadFile 成功!');
    console.log('临时文件路径:', res.tempFilePath);
    console.log('状态码:', res.statusCode);
    
    // 检查临时文件是否存在
    wx.getFileSystemManager().access({
      path: res.tempFilePath,
      success: () => {
        console.log('✅ 临时文件确认存在');
        
        // 获取文件信息
        wx.getFileSystemManager().getFileInfo({
          filePath: res.tempFilePath,
          success: (fileInfo) => {
            console.log('✅ 文件信息:');
            console.log('文件大小:', fileInfo.size, 'bytes');
            console.log('文件摘要:', fileInfo.digest);
            
            // 测试复制文件到目标位置
            const targetPath = \`\${wx.env.USER_DATA_PATH}/test_\${Date.now()}.png\`;
            console.log('目标路径:', targetPath);
            
            wx.getFileSystemManager().copyFile({
              srcPath: res.tempFilePath,
              destPath: targetPath,
              success: () => {
                console.log('✅ 文件复制成功!');
                console.log('目标文件:', targetPath);
                
                // 验证复制的文件
                wx.getFileSystemManager().getFileInfo({
                  filePath: targetPath,
                  success: (targetFileInfo) => {
                    console.log('✅ 目标文件验证成功:');
                    console.log('目标文件大小:', targetFileInfo.size, 'bytes');
                    console.log('文件完整性:', targetFileInfo.size === fileInfo.size ? '✅一致' : '❌不一致');
                  },
                  fail: (err) => {
                    console.log('❌ 目标文件验证失败:', err);
                  }
                });
              },
              fail: (copyErr) => {
                console.log('❌ 文件复制失败:', copyErr);
                console.log('错误详情:', JSON.stringify(copyErr, null, 2));
              }
            });
          },
          fail: (err) => {
            console.log('❌ 文件信息获取失败:', err);
          }
        });
      },
      fail: (accessErr) => {
        console.log('❌ 临时文件不存在:', accessErr);
        console.log('这可能是问题的根源!');
      }
    });
  },
  fail: (err) => {
    console.log('❌ wx.cloud.downloadFile 失败:', err);
    console.log('错误详情:', JSON.stringify(err, null, 2));
  }
});

// 测试文件系统权限
console.log('\\n📋 测试文件系统权限');
console.log('用户数据目录:', wx.env.USER_DATA_PATH);

// 尝试在用户数据目录创建测试文件
const testContent = 'Hello, World!';
const testFilePath = \`\${wx.env.USER_DATA_PATH}/permission_test.txt\`;

wx.getFileSystemManager().writeFile({
  filePath: testFilePath,
  data: testContent,
  encoding: 'utf8',
  success: () => {
    console.log('✅ 文件写入权限正常');
    console.log('测试文件:', testFilePath);
    
    // 清理测试文件
    wx.getFileSystemManager().unlink({
      filePath: testFilePath,
      success: () => console.log('✅ 测试文件已清理'),
      fail: (err) => console.log('⚠️ 测试文件清理失败:', err)
    });
  },
  fail: (err) => {
    console.log('❌ 文件写入权限异常:', err);
  }
});

console.log('\\n🎯 测试完成，请观察结果');
console.log('关键观察点:');
console.log('1. wx.cloud.downloadFile 是否成功？');
console.log('2. 临时文件是否存在？');
console.log('3. 文件复制是否成功？');
console.log('4. 文件系统权限是否正常？');
`

console.log(testCode)

console.log('\n📝 预期结果:')
console.log('✅ wx.cloud.downloadFile 成功')
console.log('✅ 临时文件确认存在')
console.log('✅ 文件复制成功')
console.log('✅ 文件系统权限正常')

console.log('\n🔧 如果仍有问题:')
console.log('- 检查临时文件是否真的存在')
console.log('- 检查文件系统权限')
console.log('- 考虑使用临时文件路径而不是复制')

console.log('\n请执行测试并告诉我结果！')
