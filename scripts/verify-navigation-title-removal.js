// 验证导航栏标题删除的脚本
const fs = require('fs');
const path = require('path');

console.log('📱 导航栏标题删除验证');
console.log('='.repeat(50));

// 1. 检查全局配置
console.log('\n🌐 全局导航栏配置检查:');

try {
  const appJsonPath = path.join(__dirname, '../miniprogram/app.json');
  const appJsonContent = fs.readFileSync(appJsonPath, 'utf8');
  const appConfig = JSON.parse(appJsonContent);
  
  const globalTitle = appConfig.window.navigationBarTitleText;
  console.log(`全局标题: "${globalTitle}"`);
  
  if (globalTitle === "") {
    console.log('✅ 全局导航栏标题已成功删除');
  } else {
    console.log(`❌ 全局导航栏标题仍为: "${globalTitle}"`);
  }
  
  // 检查其他全局配置
  console.log(`背景色: ${appConfig.window.navigationBarBackgroundColor}`);
  console.log(`文字样式: ${appConfig.window.navigationBarTextStyle}`);
  
} catch (error) {
  console.log('❌ 无法读取全局配置文件');
}

// 2. 检查各页面的导航栏配置
console.log('\n📄 各页面导航栏配置检查:');

const pages = [
  {
    name: '首页',
    path: 'miniprogram/pages/index/index.json',
    shouldBeEmpty: true
  },
  {
    name: '听音辩图',
    path: 'miniprogram/pages/hearing-test/hearing-test.json',
    shouldBeEmpty: false,
    expectedTitle: '听音辩图'
  },
  {
    name: 'AI助手',
    path: 'miniprogram/pages/ai-health/ai-health.json',
    shouldBeEmpty: false,
    expectedTitle: 'AI听健助手'
  },
  {
    name: '噪音测试',
    path: 'miniprogram/pages/noise-test/noise-test.json',
    shouldBeEmpty: false,
    expectedTitle: '噪音测试'
  },
  {
    name: '测试结果',
    path: 'miniprogram/pages/test-result/test-result.json',
    shouldBeEmpty: false,
    expectedTitle: '测试报告'
  },
  {
    name: '测试历史',
    path: 'miniprogram/pages/test-history/test-history.json',
    shouldBeEmpty: false,
    expectedTitle: '测试历史'
  }
];

pages.forEach(page => {
  try {
    const pagePath = path.join(__dirname, '..', page.path);
    
    if (fs.existsSync(pagePath)) {
      const pageContent = fs.readFileSync(pagePath, 'utf8');
      const pageConfig = JSON.parse(pageContent);
      
      const title = pageConfig.navigationBarTitleText || '';
      
      console.log(`\n📱 ${page.name}:`);
      console.log(`  标题: "${title}"`);
      
      if (page.shouldBeEmpty) {
        if (title === '') {
          console.log('  ✅ 标题已成功删除');
        } else {
          console.log(`  ❌ 标题应为空，但当前为: "${title}"`);
        }
      } else {
        if (title === page.expectedTitle) {
          console.log(`  ✅ 标题正确: "${title}"`);
        } else {
          console.log(`  ⚠️ 标题为: "${title}" (期望: "${page.expectedTitle}")`);
        }
      }
      
      // 检查导航栏样式
      if (pageConfig.navigationBarBackgroundColor) {
        console.log(`  背景色: ${pageConfig.navigationBarBackgroundColor}`);
      }
      if (pageConfig.navigationBarTextStyle) {
        console.log(`  文字样式: ${pageConfig.navigationBarTextStyle}`);
      }
      
    } else {
      console.log(`\n📱 ${page.name}: 文件不存在`);
    }
    
  } catch (error) {
    console.log(`\n📱 ${page.name}: 无法读取配置文件`);
  }
});

// 3. 导航栏设计分析
console.log('\n🎨 导航栏设计分析:');

const designAnalysis = [
  {
    aspect: '首页导航栏',
    before: '显示"AI听健"标题',
    after: '无标题，纯净白色导航栏',
    benefit: '更加简洁，突出页面内容'
  },
  {
    aspect: '功能页面',
    before: '各自显示功能标题',
    after: '保持功能标题不变',
    benefit: '用户清楚当前所在页面'
  },
  {
    aspect: '整体风格',
    before: '标准小程序导航栏',
    after: '简约现代的导航设计',
    benefit: '提升视觉体验和品牌感'
  }
];

designAnalysis.forEach(analysis => {
  console.log(`\n🎯 ${analysis.aspect}:`);
  console.log(`  修改前: ${analysis.before}`);
  console.log(`  修改后: ${analysis.after}`);
  console.log(`  优势: ${analysis.benefit}`);
});

// 4. 用户体验影响
console.log('\n👥 用户体验影响:');

const uxImpacts = [
  '首页更加简洁，用户注意力集中在主要功能上',
  '减少视觉干扰，提升界面的现代感',
  '功能页面保持标题，用户不会迷失方向',
  '整体设计更加统一和专业',
  '符合极简主义设计趋势'
];

uxImpacts.forEach((impact, index) => {
  console.log(`${index + 1}. ${impact}`);
});

// 5. 技术实现总结
console.log('\n🔧 技术实现总结:');

const technicalSummary = [
  '修改全局app.json中的navigationBarTitleText为空字符串',
  '修改首页index.json中的navigationBarTitleText为空字符串',
  '保持其他功能页面的标题不变',
  '维持白色背景和黑色文字的导航栏样式',
  '确保所有页面的导航栏样式一致'
];

technicalSummary.forEach((item, index) => {
  console.log(`${index + 1}. ${item}`);
});

// 6. 验证结果
console.log('\n📊 验证结果:');

try {
  const appJsonPath = path.join(__dirname, '../miniprogram/app.json');
  const indexJsonPath = path.join(__dirname, '../miniprogram/pages/index/index.json');
  
  const appConfig = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
  const indexConfig = JSON.parse(fs.readFileSync(indexJsonPath, 'utf8'));
  
  const globalTitleEmpty = appConfig.window.navigationBarTitleText === '';
  const indexTitleEmpty = indexConfig.navigationBarTitleText === '';
  
  console.log(`✅ 全局标题删除: ${globalTitleEmpty ? '成功' : '失败'}`);
  console.log(`✅ 首页标题删除: ${indexTitleEmpty ? '成功' : '失败'}`);
  console.log(`✅ 导航栏背景: ${appConfig.window.navigationBarBackgroundColor}`);
  console.log(`✅ 导航栏文字: ${appConfig.window.navigationBarTextStyle}`);
  
  if (globalTitleEmpty && indexTitleEmpty) {
    console.log('\n🎉 导航栏标题删除完全成功！');
  } else {
    console.log('\n⚠️ 部分配置可能需要进一步检查');
  }
  
} catch (error) {
  console.log('\n❌ 验证过程中出现错误');
}

console.log('\n' + '='.repeat(50));
console.log('🚀 现在首页拥有简洁的无标题导航栏！');
