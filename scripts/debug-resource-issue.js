// 调试资源问题的脚本
console.log('🔍 调试资源下载问题');

// 测试路径列表
const testPaths = [
  'resources/images/wordlist/冰箱.png',
  'resources/audio/female/冰箱.mp3',
  'resources/audio/male/冰箱.mp3'
];

console.log('\n📋 测试路径列表:');
testPaths.forEach((path, index) => {
  console.log(`${index + 1}. ${path}`);
});

// 分析可能的问题
console.log('\n🔍 可能的问题分析:');

console.log('\n1. 路径格式问题:');
console.log('   - 云存储路径应该以 "resources/" 开头');
console.log('   - 文件名包含中文字符，可能需要编码');

console.log('\n2. 权限问题:');
console.log('   - 云函数可能没有访问云存储的权限');
console.log('   - 需要检查云函数的服务授权');

console.log('\n3. 环境问题:');
console.log('   - 云函数和云存储是否在同一个环境');
console.log('   - 环境ID: cloud1-0gjev5gfdef4d262');

console.log('\n4. 文件编码问题:');
console.log('   - 中文文件名可能需要URL编码');
console.log('   - 测试编码后的路径');

// 测试URL编码
console.log('\n🔤 URL编码测试:');
testPaths.forEach(path => {
  const encoded = encodeURIComponent(path);
  console.log(`原始: ${path}`);
  console.log(`编码: ${encoded}`);
  console.log('---');
});

// 建议的解决方案
console.log('\n💡 建议的解决方案:');

console.log('\n1. 检查云函数权限:');
console.log('   - 在微信开发者工具中右键云函数');
console.log('   - 选择"云端安装依赖"');
console.log('   - 确保云函数有云存储访问权限');

console.log('\n2. 测试简单路径:');
console.log('   - 先测试英文文件名');
console.log('   - 确认基本功能正常');

console.log('\n3. 添加更多调试信息:');
console.log('   - 在云函数中打印详细的错误信息');
console.log('   - 检查getTempFileURL的返回结果');

console.log('\n4. 检查环境配置:');
console.log('   - 确认云函数和云存储在同一环境');
console.log('   - 检查环境ID是否正确');

// 生成测试命令
console.log('\n🧪 手动测试命令:');
console.log('在微信开发者工具的控制台中执行:');

const testCode = `
// 测试云函数调用
wx.cloud.callFunction({
  name: 'downloadResource',
  data: {
    cloudPath: 'resources/images/wordlist/冰箱.png',
    type: 'image'
  },
  success: (res) => {
    console.log('云函数调用成功:', res);
  },
  fail: (err) => {
    console.error('云函数调用失败:', err);
  }
});
`;

console.log(testCode);

console.log('\n📝 调试步骤:');
console.log('1. 在微信开发者工具中打开项目');
console.log('2. 在控制台中执行上述测试代码');
console.log('3. 查看云函数日志');
console.log('4. 检查返回的错误信息');

console.log('\n🔗 相关链接:');
console.log('- 云函数控制台: https://console.cloud.tencent.com/tcb/scf');
console.log('- 云存储控制台: https://console.cloud.tencent.com/tcb/storage');
console.log('- 环境概览: https://console.cloud.tencent.com/tcb/env/overview');
