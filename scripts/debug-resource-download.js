// 调试资源下载问题的脚本
const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-0gjev5gfdef4d262'
})

async function testResourceDownload() {
  try {
    console.log('🔍 开始调试资源下载问题...')
    
    // 1. 测试getResourceList云函数
    console.log('\n📋 测试getResourceList云函数...')
    const getResourceListResult = await cloud.callFunction({
      name: 'getResourceList',
      data: {}
    })
    
    console.log('getResourceList返回结果:', JSON.stringify(getResourceListResult.result, null, 2))
    
    if (!getResourceListResult.result.success) {
      console.error('❌ getResourceList失败:', getResourceListResult.result.error)
      return
    }
    
    const { audio, image } = getResourceListResult.result.data
    console.log(`📊 资源统计: 音频${audio.length}个, 图片${image.length}个`)
    
    // 2. 测试云存储文件是否存在
    console.log('\n🔍 检查云存储文件是否存在...')
    
    // 测试几个关键文件
    const testFiles = [
      audio[0]?.cloudPath, // 第一个音频文件
      image[0]?.cloudPath  // 第一个图片文件
    ].filter(Boolean)
    
    for (const cloudPath of testFiles) {
      try {
        console.log(`检查文件: ${cloudPath}`)
        
        const tempFileResult = await cloud.getTempFileURL({
          fileList: [cloudPath]
        })
        
        console.log(`临时链接结果:`, tempFileResult)
        
        if (tempFileResult.fileList[0].tempFileURL) {
          console.log(`✅ 文件存在: ${cloudPath}`)
        } else {
          console.log(`❌ 文件不存在: ${cloudPath}`)
          console.log(`错误信息:`, tempFileResult.fileList[0])
        }
      } catch (error) {
        console.error(`❌ 检查文件失败: ${cloudPath}`, error)
      }
    }
    
    // 3. 列出云存储中的实际文件
    console.log('\n📁 列出云存储中的实际文件...')
    try {
      // 列出resources目录下的文件
      const storage = cloud.storage()
      
      // 尝试列出audio目录
      console.log('检查audio目录...')
      const audioFiles = await storage.listFile({
        prefix: 'resources/audio/',
        maxKeys: 10
      })
      console.log('audio目录文件:', audioFiles.fileList.map(f => f.fileID))
      
      // 尝试列出images目录
      console.log('检查images目录...')
      const imageFiles = await storage.listFile({
        prefix: 'resources/images/',
        maxKeys: 10
      })
      console.log('images目录文件:', imageFiles.fileList.map(f => f.fileID))
      
    } catch (error) {
      console.error('❌ 列出文件失败:', error)
    }
    
  } catch (error) {
    console.error('❌ 调试失败:', error)
  }
}

// 运行调试
testResourceDownload()
