// 验证噪音测试详细结果修复的脚本
const fs = require('fs');
const path = require('path');

console.log('📊 噪音测试详细结果修复验证');
console.log('='.repeat(50));

// 1. 检查噪音测试数据结构
console.log('\n🔊 噪音测试数据结构检查:');

try {
  const noiseTestJsPath = path.join(__dirname, '../miniprogram/pages/noise-test/noise-test.js');
  const noiseTestJsContent = fs.readFileSync(noiseTestJsPath, 'utf8');
  
  // 检查级别结果对象结构
  const levelResultMatch = noiseTestJsContent.match(/const\s+levelResult\s*=\s*{[\s\S]*?accuracy:\s*accuracy[\s\S]*?}/);
  if (levelResultMatch) {
    console.log('✅ 噪音测试级别结果对象结构正确');
    console.log('   包含字段: level, name, results, correctCount, totalCount, accuracy');
  } else {
    console.log('❌ 噪音测试级别结果对象结构不正确');
  }
  
  // 检查数据传递
  const navigateMatch = noiseTestJsContent.match(/results=\$\{JSON\.stringify\(testResults\)\}/);
  if (navigateMatch) {
    console.log('✅ 正确传递testResults数据到结果页面');
  } else {
    console.log('❌ 未正确传递testResults数据');
  }
  
} catch (error) {
  console.log('❌ 无法读取噪音测试JS文件');
}

// 2. 检查听音辨图数据结构作为对比
console.log('\n🎧 听音辨图数据结构对比:');

try {
  const hearingTestJsPath = path.join(__dirname, '../miniprogram/pages/hearing-test/hearing-test.js');
  const hearingTestJsContent = fs.readFileSync(hearingTestJsPath, 'utf8');
  
  // 检查听音辨图的级别结果对象
  const hearingLevelResultMatch = hearingTestJsContent.match(/const\s+levelResult\s*=\s*{[\s\S]*?recognitionRate:\s*recognitionRate[\s\S]*?}/);
  if (hearingLevelResultMatch) {
    console.log('✅ 听音辨图级别结果对象结构');
    console.log('   包含字段: level, results, recognitionRate, recognitionRateFormatted, correctCount, totalCount');
  } else {
    console.log('❌ 听音辨图级别结果对象结构异常');
  }
  
} catch (error) {
  console.log('❌ 无法读取听音辨图JS文件');
}

// 3. 检查test-result页面的数据转换逻辑
console.log('\n📋 test-result页面数据转换检查:');

try {
  const testResultJsPath = path.join(__dirname, '../miniprogram/pages/test-result/test-result.js');
  const testResultJsContent = fs.readFileSync(testResultJsPath, 'utf8');
  
  // 检查数据转换逻辑
  const dataConversionMatch = testResultJsContent.match(/testResults\s*=\s*rawResults\.map\s*\(\s*item\s*=>\s*{[\s\S]*?recognitionRate:\s*accuracyNum\.toFixed\(1\)[\s\S]*?}\s*\)/);
  if (dataConversionMatch) {
    console.log('✅ 噪音测试数据转换逻辑存在');
    console.log('   转换: accuracy -> recognitionRate');
  } else {
    console.log('❌ 缺少噪音测试数据转换逻辑');
  }
  
  // 检查字段映射
  const fieldMappingChecks = [
    { field: 'recognitionRate', found: testResultJsContent.includes('recognitionRate: accuracyNum.toFixed(1)') },
    { field: 'recognitionRateNum', found: testResultJsContent.includes('recognitionRateNum: accuracyNum') },
    { field: 'correctCount', found: testResultJsContent.includes('correctCount: item.correctCount') },
    { field: 'totalCount', found: testResultJsContent.includes('totalCount: item.totalCount') }
  ];
  
  console.log('\n字段映射检查:');
  fieldMappingChecks.forEach(check => {
    console.log(`${check.found ? '✅' : '❌'} ${check.field}: ${check.found ? '正确映射' : '缺失映射'}`);
  });
  
} catch (error) {
  console.log('❌ 无法读取test-result JS文件');
}

// 4. 检查WXML显示逻辑
console.log('\n📄 WXML显示逻辑检查:');

try {
  const testResultWxmlPath = path.join(__dirname, '../miniprogram/pages/test-result/test-result.wxml');
  const testResultWxmlContent = fs.readFileSync(testResultWxmlPath, 'utf8');
  
  // 检查准确率显示
  const rateDisplayMatch = testResultWxmlContent.match(/准确率:\s*\{\{item\.recognitionRate\}\}%/);
  if (rateDisplayMatch) {
    console.log('✅ 准确率显示逻辑正确');
    console.log('   显示: {{item.recognitionRate}}%');
  } else {
    console.log('❌ 准确率显示逻辑不正确');
  }
  
  // 检查进度条
  const progressMatch = testResultWxmlContent.match(/width:\s*\{\{item\.recognitionRateNum\s*\|\|\s*0\}\}%/);
  if (progressMatch) {
    console.log('✅ 进度条逻辑正确');
    console.log('   宽度: {{item.recognitionRateNum || 0}}%');
  } else {
    console.log('❌ 进度条逻辑不正确');
  }
  
  // 检查正确数量显示
  const correctDisplayMatch = testResultWxmlContent.match(/正确识别:\s*\{\{item\.correctCount\s*\|\|\s*0\}\}\/\{\{item\.totalCount\s*\|\|\s*0\}\}/);
  if (correctDisplayMatch) {
    console.log('✅ 正确数量显示逻辑正确');
  } else {
    console.log('❌ 正确数量显示逻辑不正确');
  }
  
} catch (error) {
  console.log('❌ 无法读取test-result WXML文件');
}

// 5. 数据结构对比分析
console.log('\n🔍 数据结构对比分析:');

const dataStructureComparison = {
  hearing: {
    name: '听音辨图',
    fields: {
      level: 'item.level (声强级别)',
      rate: 'item.recognitionRate (识别率)',
      rateNum: 'item.recognitionRateNum (数字格式)',
      correct: 'item.correctCount (正确数量)',
      total: 'item.totalCount (总数量)'
    }
  },
  noise: {
    name: '噪音测试',
    originalFields: {
      level: 'item.level (噪音级别)',
      rate: 'item.accuracy (准确率)',
      correct: 'item.correctCount (正确数量)',
      total: 'item.totalCount (总数量)'
    },
    convertedFields: {
      level: 'item.level (噪音级别)',
      rate: 'item.recognitionRate (转换后的识别率)',
      rateNum: 'item.recognitionRateNum (数字格式)',
      correct: 'item.correctCount (正确数量)',
      total: 'item.totalCount (总数量)'
    }
  }
};

console.log('听音辨图数据结构:');
Object.entries(dataStructureComparison.hearing.fields).forEach(([key, value]) => {
  console.log(`  ${key}: ${value}`);
});

console.log('\n噪音测试原始数据结构:');
Object.entries(dataStructureComparison.noise.originalFields).forEach(([key, value]) => {
  console.log(`  ${key}: ${value}`);
});

console.log('\n噪音测试转换后数据结构:');
Object.entries(dataStructureComparison.noise.convertedFields).forEach(([key, value]) => {
  console.log(`  ${key}: ${value}`);
});

// 6. 转换逻辑分析
console.log('\n🔄 转换逻辑分析:');

const conversionLogic = [
  {
    step: 1,
    action: '解析URL参数中的results',
    input: 'JSON.stringify(testResults)',
    output: 'rawResults数组'
  },
  {
    step: 2,
    action: '遍历每个级别结果',
    input: 'rawResults.map(item => {...})',
    output: '转换后的testResults数组'
  },
  {
    step: 3,
    action: '字段转换',
    input: 'item.accuracy',
    output: 'recognitionRate: accuracyNum.toFixed(1)'
  },
  {
    step: 4,
    action: '数字格式保留',
    input: 'parseFloat(item.accuracy)',
    output: 'recognitionRateNum: accuracyNum'
  },
  {
    step: 5,
    action: '其他字段保持',
    input: 'item.correctCount, item.totalCount',
    output: '直接映射到新对象'
  }
];

conversionLogic.forEach(logic => {
  console.log(`\n步骤${logic.step}: ${logic.action}`);
  console.log(`  输入: ${logic.input}`);
  console.log(`  输出: ${logic.output}`);
});

// 7. 问题解决方案总结
console.log('\n💡 问题解决方案总结:');

const solutionSummary = [
  {
    problem: '噪音测试使用accuracy字段，test-result页面期望recognitionRate字段',
    solution: '在handleNoiseTestResult方法中进行数据转换'
  },
  {
    problem: '数据格式不兼容导致显示为空',
    solution: '将accuracy转换为recognitionRate格式，保持小数点后1位'
  },
  {
    problem: '进度条需要数字格式的百分比',
    solution: '同时提供recognitionRateNum字段用于进度条宽度计算'
  },
  {
    problem: '需要保持与听音辨图页面的显示一致性',
    solution: '转换后的数据结构完全兼容现有的WXML显示逻辑'
  }
];

solutionSummary.forEach((solution, index) => {
  console.log(`\n${index + 1}. 问题: ${solution.problem}`);
  console.log(`   解决: ${solution.solution}`);
});

// 8. 测试验证步骤
console.log('\n🧪 测试验证步骤:');

const testSteps = [
  '完成一次噪音测试，确保每个级别都有测试数据',
  '点击"查看详细结果"按钮',
  '检查是否正确显示"噪音测试报告"标题',
  '验证每个分贝级别是否显示正确的准确率百分比',
  '检查进度条是否正确显示对应的宽度',
  '验证"正确识别: X/Y 个词语"是否显示正确',
  '对比听音辨图测试报告，确保显示格式一致'
];

testSteps.forEach((step, index) => {
  console.log(`${index + 1}. ${step}`);
});

console.log('\n' + '='.repeat(50));
console.log('🎉 噪音测试详细结果修复验证完成！');

// 9. 预期结果
console.log('\n📊 预期结果:');
console.log('✅ 30dB级别显示准确率: XX.X%');
console.log('✅ 20dB级别显示准确率: XX.X%');
console.log('✅ 10dB级别显示准确率: XX.X%');
console.log('✅ 进度条正确显示对应宽度');
console.log('✅ 正确识别数量正确显示');
console.log('✅ 整体显示格式与听音辨图一致');

console.log('\n🚀 现在噪音测试详细结果应该正确显示了！');
