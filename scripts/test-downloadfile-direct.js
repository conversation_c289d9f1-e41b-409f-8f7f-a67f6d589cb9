// 直接测试 downloadFile 功能
const cloud = require('wx-server-sdk')

// 设置环境变量
process.env.TENCENT_SECRET_ID = "AKIDpZ8YpzxvyueFeqr7C4jBRUp24tZ3NVUP"
process.env.TENCENT_SECRET_KEY = "ALQK1jPwIVaUBj8NckjX8XpywiLo58IK"

cloud.init({
  env: 'cloud1-0gjev5gfdef4d262'
})

async function testDownloadFile() {
  try {
    console.log('🔍 开始测试 downloadFile 功能...')
    
    // 测试1: 调用 getResourceList 云函数
    console.log('\n📋 测试1: getResourceList云函数')
    const getResourceListResult = await cloud.callFunction({
      name: 'getResourceList',
      data: {}
    })
    
    console.log('getResourceList结果:', getResourceListResult.result.success ? '✅成功' : '❌失败')
    
    if (getResourceListResult.result.success) {
      const { audio, image } = getResourceListResult.result.data
      console.log(`资源统计: 音频${audio.length}个, 图片${image.length}个`)
      
      // 检查返回的路径格式
      if (image.length > 0) {
        const testImage = image.find(item => item.word === '冰箱') || image[0]
        console.log('测试图片路径:', testImage.cloudPath)
        console.log('路径格式正确:', testImage.cloudPath.startsWith('cloud://') ? '✅是' : '❌否')
        
        // 测试2: 使用云函数模拟 downloadFile
        console.log(`\n📋 测试2: 模拟 downloadFile - ${testImage.word}`)
        
        try {
          // 在云函数环境中，我们可以使用 getTempFileURL 来验证文件是否可访问
          const tempResult = await cloud.getTempFileURL({
            fileList: [testImage.cloudPath]
          })
          
          console.log('getTempFileURL结果:', JSON.stringify(tempResult, null, 2))
          
          if (tempResult.fileList && tempResult.fileList.length > 0) {
            const fileInfo = tempResult.fileList[0]
            if (fileInfo.status === 0 && fileInfo.tempFileURL) {
              console.log('✅ 文件可访问!')
              console.log('临时链接:', fileInfo.tempFileURL)
            } else {
              console.log('❌ 文件不可访问:', fileInfo.errMsg)
            }
          }
        } catch (error) {
          console.log('❌ getTempFileURL失败:', error.message)
        }
        
        // 测试3: 批量测试多个文件
        console.log('\n📋 测试3: 批量测试多个文件')
        const testFiles = [
          image.find(item => item.word === '书本'),
          audio.find(item => item.word === '冰箱' && item.gender === 'female'),
          audio.find(item => item.word === '窗户' && item.gender === 'male')
        ].filter(Boolean)
        
        for (const file of testFiles) {
          console.log(`\n批量测试: ${file.word} (${file.gender || 'image'})`)
          console.log(`fileID: ${file.cloudPath}`)
          
          try {
            const result = await cloud.getTempFileURL({
              fileList: [file.cloudPath]
            })
            
            const fileInfo = result.fileList[0]
            if (fileInfo.status === 0 && fileInfo.tempFileURL) {
              console.log(`✅ ${file.word} 可访问`)
            } else {
              console.log(`❌ ${file.word} 不可访问: ${fileInfo.errMsg}`)
            }
          } catch (error) {
            console.log(`❌ ${file.word} 测试失败: ${error.message}`)
          }
        }
      }
      
    } else {
      console.log('❌ getResourceList失败:', getResourceListResult.result.error)
    }
    
    console.log('\n🎯 测试完成!')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
  }
}

// 运行测试
testDownloadFile()
