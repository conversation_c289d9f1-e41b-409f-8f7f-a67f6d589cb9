// 验证测试特点颜色修复的脚本
const fs = require('fs');
const path = require('path');

console.log('🔧 测试特点颜色修复验证');
console.log('='.repeat(50));

// 1. 检查主题文件中的CSS变量
console.log('\n🎨 主题文件CSS变量检查:');

try {
  const themePath = path.join(__dirname, '../miniprogram/styles/theme.wxss');
  const themeContent = fs.readFileSync(themePath, 'utf8');
  
  // 检查--text-primary变量
  const textPrimaryMatch = themeContent.match(/--text-primary:\s*(#[0-9a-fA-F]{6})/);
  if (textPrimaryMatch) {
    const color = textPrimaryMatch[1];
    console.log(`✅ --text-primary变量: ${color}`);
    
    if (color === '#374151') {
      console.log('   ✅ 已更新为深灰色，不再是纯黑色');
    } else if (color === '#0f172a') {
      console.log('   ❌ 仍然是纯黑色，需要修复');
    } else {
      console.log(`   ⚠️ 使用了其他颜色: ${color}`);
    }
  } else {
    console.log('❌ 未找到--text-primary变量定义');
  }
  
  // 检查是否还有其他纯黑色定义
  const pureBlackMatches = themeContent.match(/#0f172a/g);
  if (pureBlackMatches) {
    console.log(`⚠️ 主题文件中仍有 ${pureBlackMatches.length} 处使用纯黑色`);
  } else {
    console.log('✅ 主题文件中已无纯黑色定义');
  }
  
} catch (error) {
  console.log('❌ 无法读取主题文件');
}

// 2. 检查首页样式文件
console.log('\n📄 首页样式文件检查:');

try {
  const indexWxssPath = path.join(__dirname, '../miniprogram/pages/index/index.wxss');
  const indexWxssContent = fs.readFileSync(indexWxssPath, 'utf8');
  
  // 检查info-title样式
  const infoTitleMatch = indexWxssContent.match(/\.info-title\s*{[^}]*color:\s*(#[0-9a-fA-F]{6})[^}]*}/);
  if (infoTitleMatch) {
    const color = infoTitleMatch[1];
    console.log(`✅ .info-title颜色: ${color}`);
    
    if (color === '#374151') {
      console.log('   ✅ 使用深灰色，符合预期');
    } else {
      console.log(`   ⚠️ 使用了其他颜色: ${color}`);
    }
  } else {
    console.log('❌ 未找到.info-title样式定义');
  }
  
  // 检查是否有!important规则
  const importantMatch = indexWxssContent.match(/\.info-title[^}]*color:[^}]*!important/);
  if (importantMatch) {
    console.log('✅ 已添加!important规则，确保样式优先级');
  } else {
    console.log('⚠️ 未找到!important规则');
  }
  
  // 检查是否有更具体的选择器
  const specificMatch = indexWxssContent.match(/\.info-card\s+\.info-title/);
  if (specificMatch) {
    console.log('✅ 已添加更具体的选择器规则');
  } else {
    console.log('⚠️ 未找到更具体的选择器');
  }
  
} catch (error) {
  console.log('❌ 无法读取首页样式文件');
}

// 3. 检查WXML结构
console.log('\n📋 WXML结构检查:');

try {
  const indexWxmlPath = path.join(__dirname, '../miniprogram/pages/index/index.wxml');
  const indexWxmlContent = fs.readFileSync(indexWxmlPath, 'utf8');
  
  // 检查测试特点标题的类名
  const testFeaturesMatch = indexWxmlContent.match(/<text\s+class="info-title">测试特点<\/text>/);
  if (testFeaturesMatch) {
    console.log('✅ 测试特点标题使用正确的CSS类名: info-title');
  } else {
    console.log('❌ 测试特点标题未使用正确的CSS类名');
  }
  
  // 检查是否在正确的容器中
  const containerMatch = indexWxmlContent.match(/<view\s+class="info-card">[\s\S]*<text\s+class="info-title">测试特点<\/text>/);
  if (containerMatch) {
    console.log('✅ 测试特点标题在正确的info-card容器中');
  } else {
    console.log('⚠️ 测试特点标题可能不在预期的容器中');
  }
  
} catch (error) {
  console.log('❌ 无法读取WXML文件');
}

// 4. 样式优先级分析
console.log('\n🎯 样式优先级分析:');

const priorityAnalysis = [
  {
    selector: '.info-title',
    specificity: '0,0,1,0',
    priority: '低',
    description: '基础类选择器'
  },
  {
    selector: '.info-card .info-title',
    specificity: '0,0,2,0',
    priority: '中',
    description: '更具体的类选择器组合'
  },
  {
    selector: '.info-title !important',
    specificity: '1,0,1,0',
    priority: '高',
    description: '带!important的类选择器'
  },
  {
    selector: 'var(--text-primary)',
    specificity: '继承',
    priority: '最低',
    description: 'CSS变量，可被覆盖'
  }
];

priorityAnalysis.forEach(analysis => {
  console.log(`\n📐 ${analysis.selector}:`);
  console.log(`  特异性: ${analysis.specificity}`);
  console.log(`  优先级: ${analysis.priority}`);
  console.log(`  说明: ${analysis.description}`);
});

// 5. 可能的问题和解决方案
console.log('\n🔍 可能的问题和解决方案:');

const troubleshooting = [
  {
    problem: '小程序缓存',
    solution: '清除小程序缓存，重新编译项目',
    likelihood: '高'
  },
  {
    problem: 'CSS变量覆盖',
    solution: '更新theme.wxss中的--text-primary变量',
    likelihood: '高'
  },
  {
    problem: '样式优先级',
    solution: '使用!important或更具体的选择器',
    likelihood: '中'
  },
  {
    problem: '样式文件未加载',
    solution: '检查@import路径和文件存在性',
    likelihood: '低'
  },
  {
    problem: '开发工具问题',
    solution: '重启微信开发者工具',
    likelihood: '低'
  }
];

troubleshooting.forEach(item => {
  console.log(`\n🐛 ${item.problem} (可能性: ${item.likelihood}):`);
  console.log(`  解决方案: ${item.solution}`);
});

// 6. 修复步骤总结
console.log('\n✅ 修复步骤总结:');

const fixSteps = [
  '更新theme.wxss中的--text-primary从#0f172a改为#374151',
  '在index.wxss中为.info-title添加!important规则',
  '添加更具体的选择器.info-card .info-title',
  '确保WXML中使用正确的CSS类名info-title',
  '清除小程序缓存并重新编译'
];

fixSteps.forEach((step, index) => {
  console.log(`${index + 1}. ${step}`);
});

// 7. 验证建议
console.log('\n🧪 验证建议:');

const verificationSteps = [
  '在微信开发者工具中清除缓存',
  '重新编译整个项目',
  '检查调试器中的元素样式',
  '确认计算后的样式值',
  '在真机上测试效果',
  '如果仍有问题，尝试重启开发者工具'
];

verificationSteps.forEach((step, index) => {
  console.log(`${index + 1}. ${step}`);
});

console.log('\n' + '='.repeat(50));
console.log('🎉 测试特点颜色修复验证完成！');

// 8. 最终状态
console.log('\n📊 预期最终状态:');
console.log('✅ 主题变量: --text-primary = #374151');
console.log('✅ 样式规则: .info-title color = #374151 !important');
console.log('✅ 选择器: .info-card .info-title 更具体');
console.log('✅ 显示效果: 测试特点标题为深灰色');

console.log('\n🚀 现在测试特点应该显示为美观的深灰色！');
