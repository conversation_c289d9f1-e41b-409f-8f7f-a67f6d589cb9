// 测试缓存验证功能的脚本
console.log('🔍 测试缓存验证功能');

const testCode = `
// 在微信开发者工具控制台中执行
const resourceManager = require('./utils/resourceManager');

console.log('🔍 开始测试缓存验证...');

// 1. 检查当前缓存状态（新的验证逻辑）
console.log('📦 检查缓存状态（带文件验证）...');
const cacheStatus = resourceManager.getCacheStatus();
console.log('📊 缓存状态结果:', cacheStatus);

// 2. 手动检查几个关键文件
console.log('🔍 手动检查关键文件...');
const testWords = ['书本', '冰箱', '回家'];

testWords.forEach(word => {
  console.log(\`\\n📁 检查文件: \${word}\`);
  
  // 检查音频文件
  try {
    const femalePath = resourceManager.getLocalResourcePath(word, 'audio', 'female');
    const malePath = resourceManager.getLocalResourcePath(word, 'audio', 'male');
    
    console.log('🎵 女声路径:', femalePath);
    console.log('🎵 男声路径:', malePath);
    
    try {
      wx.getFileSystemManager().statSync(femalePath);
      console.log('✅ 女声文件存在');
    } catch (e) {
      console.log('❌ 女声文件不存在');
    }
    
    try {
      wx.getFileSystemManager().statSync(malePath);
      console.log('✅ 男声文件存在');
    } catch (e) {
      console.log('❌ 男声文件不存在');
    }
  } catch (error) {
    console.log('❌ 音频路径获取失败:', error);
  }
  
  // 检查图片文件
  try {
    const imagePath = resourceManager.getLocalResourcePath(word, 'image');
    console.log('🖼️ 图片路径:', imagePath);
    
    try {
      wx.getFileSystemManager().statSync(imagePath);
      console.log('✅ 图片文件存在');
    } catch (e) {
      console.log('❌ 图片文件不存在');
    }
  } catch (error) {
    console.log('❌ 图片路径获取失败:', error);
  }
});

// 3. 如果缓存状态不正确，手动清理并重新下载
if (!cacheStatus.isComplete) {
  console.log('\\n🚀 缓存不完整，开始下载...');
  
  resourceManager.setProgressCallback((progress, downloaded, total) => {
    console.log(\`📊 下载进度: \${progress.toFixed(1)}% (\${downloaded}/\${total})\`);
  });
  
  resourceManager.setCompleteCallback((message) => {
    if (message) {
      console.log('⚠️ 下载完成（部分失败）:', message);
    } else {
      console.log('✅ 下载完成');
      
      // 重新检查缓存状态
      const newCacheStatus = resourceManager.getCacheStatus();
      console.log('📊 下载后缓存状态:', newCacheStatus);
    }
  });
  
  resourceManager.init().then(() => {
    console.log('✅ 资源初始化完成');
  }).catch(error => {
    console.error('❌ 资源初始化失败:', error);
  });
} else {
  console.log('\\n✅ 缓存状态显示完整，但请验证测试功能是否正常');
}

// 4. 测试单个文件的路径获取
console.log('\\n🧪 测试路径获取功能...');
const testWord = '书本';
try {
  const audioPath = resourceManager.getLocalResourcePath(testWord, 'audio', 'female');
  const imagePath = resourceManager.getLocalResourcePath(testWord, 'image');
  
  console.log('📁 生成的路径:');
  console.log('  音频:', audioPath);
  console.log('  图片:', imagePath);
  
  // 检查路径格式是否正确
  if (audioPath.includes('wx.env.USER_DATA_PATH')) {
    console.log('⚠️ 音频路径包含变量名，可能有问题');
  }
  if (imagePath.includes('wx.env.USER_DATA_PATH')) {
    console.log('⚠️ 图片路径包含变量名，可能有问题');
  }
} catch (error) {
  console.error('❌ 路径获取测试失败:', error);
}
`;

console.log('\n📋 在微信开发者工具控制台中执行以下代码:');
console.log('='.repeat(60));
console.log(testCode);
console.log('='.repeat(60));

console.log('\n🎯 测试目标:');
console.log('1. 验证新的缓存状态检查是否正确识别文件不存在');
console.log('2. 确认错误的缓存状态标记会被自动清理');
console.log('3. 验证下载功能是否能正常启动');
console.log('4. 检查文件路径生成是否正确');

console.log('\n📝 预期结果:');
console.log('- 如果文件不存在，缓存状态应该变为 isComplete: false');
console.log('- 错误的缓存状态标记应该被自动清理');
console.log('- 下载应该自动启动');
console.log('- 下载完成后，测试功能应该正常工作');

console.log('\n🚨 关键检查点:');
console.log('- 看到 "⚠️ 缓存文件不存在" 说明检测到了问题');
console.log('- 看到 "🗑️ 已清理缓存状态标记" 说明自动修复了状态');
console.log('- 看到下载进度说明重新开始下载');
console.log('- 最终测试功能应该可以正常使用');
