// 测试 wx.cloud.downloadFile 在微信开发者工具中的行为
console.log('🧪 测试 wx.cloud.downloadFile 在微信开发者工具中的行为')

console.log('\n请在微信开发者工具控制台中执行以下测试代码:')

const testCode = `
console.log('🔍 测试 wx.cloud.downloadFile...');

// 测试已知存在的文件
const testFileID = 'cloud://cloud1-0gjev5gfdef4d262.636c-cloud1-0gjev5gfdef4d262-1314700961/resources/audio/male/时间.mp3';

console.log('测试文件:', testFileID);

// 方法1: 直接测试 wx.cloud.downloadFile
console.log('\\n📋 方法1: 直接测试 wx.cloud.downloadFile');

wx.cloud.downloadFile({
  fileID: testFileID,
  success: (res) => {
    console.log('✅ wx.cloud.downloadFile 成功!');
    console.log('完整响应:', res);
    console.log('临时文件路径:', res.tempFilePath);
    console.log('状态码:', res.statusCode);
    
    // 立即检查临时文件是否存在
    wx.getFileSystemManager().access({
      path: res.tempFilePath,
      success: () => {
        console.log('✅ 临时文件确认存在');
        
        // 获取文件信息
        wx.getFileSystemManager().getFileInfo({
          filePath: res.tempFilePath,
          success: (fileInfo) => {
            console.log('✅ 文件信息获取成功:');
            console.log('文件大小:', fileInfo.size, 'bytes');
            console.log('文件摘要:', fileInfo.digest);
          },
          fail: (err) => {
            console.log('❌ 文件信息获取失败:', err);
          }
        });
      },
      fail: (accessErr) => {
        console.log('❌ 临时文件不存在:', accessErr);
        console.log('这说明 wx.cloud.downloadFile 有问题');
      }
    });
  },
  fail: (err) => {
    console.log('❌ wx.cloud.downloadFile 失败:', err);
    console.log('错误详情:', JSON.stringify(err, null, 2));
  }
});

// 方法2: 对比测试 wx.cloud.getTempFileURL + wx.downloadFile
console.log('\\n📋 方法2: 对比测试 getTempFileURL + downloadFile');

wx.cloud.getTempFileURL({
  fileList: [testFileID],
  success: (tempRes) => {
    console.log('✅ getTempFileURL 成功');
    
    if (tempRes.fileList && tempRes.fileList.length > 0) {
      const fileInfo = tempRes.fileList[0];
      if (fileInfo.tempFileURL) {
        console.log('临时链接:', fileInfo.tempFileURL);
        
        // 使用临时链接下载
        wx.downloadFile({
          url: fileInfo.tempFileURL,
          success: (downloadRes) => {
            console.log('✅ wx.downloadFile 成功!');
            console.log('下载响应:', downloadRes);
            console.log('临时文件路径:', downloadRes.tempFilePath);
            
            // 检查下载的文件
            wx.getFileSystemManager().access({
              path: downloadRes.tempFilePath,
              success: () => {
                console.log('✅ 下载的临时文件存在');
                
                wx.getFileSystemManager().getFileInfo({
                  filePath: downloadRes.tempFilePath,
                  success: (fileInfo) => {
                    console.log('✅ 下载文件信息:');
                    console.log('文件大小:', fileInfo.size, 'bytes');
                  }
                });
              },
              fail: (err) => {
                console.log('❌ 下载的临时文件不存在:', err);
              }
            });
          },
          fail: (downloadErr) => {
            console.log('❌ wx.downloadFile 失败:', downloadErr);
          }
        });
      } else {
        console.log('❌ getTempFileURL 返回空链接:', fileInfo.errMsg);
      }
    }
  },
  fail: (err) => {
    console.log('❌ getTempFileURL 失败:', err);
  }
});

// 方法3: 测试环境信息
console.log('\\n📋 方法3: 测试环境信息');
console.log('当前环境:', wx.cloud.getEnvironment ? wx.cloud.getEnvironment() : '无法获取');
console.log('用户数据目录:', wx.env.USER_DATA_PATH);
console.log('临时文件目录:', wx.env.USER_DATA_PATH + '/tmp');

console.log('\\n🎯 测试完成，请观察结果');
console.log('关键对比:');
console.log('- wx.cloud.downloadFile 是否成功？');
console.log('- wx.cloud.getTempFileURL + wx.downloadFile 是否成功？');
console.log('- 哪种方法的临时文件真实存在？');
`

console.log(testCode)

console.log('\n📝 预期结果:')
console.log('如果 wx.cloud.downloadFile 有问题:')
console.log('- ❌ wx.cloud.downloadFile 失败或临时文件不存在')
console.log('- ✅ getTempFileURL + downloadFile 成功')

console.log('\n如果 wx.cloud.downloadFile 正常:')
console.log('- ✅ wx.cloud.downloadFile 成功且临时文件存在')
console.log('- ✅ 两种方法都成功')

console.log('\n🔧 根据测试结果:')
console.log('- 如果方法1失败，方法2成功 → 使用 getTempFileURL + downloadFile')
console.log('- 如果两种方法都失败 → 检查环境配置')
console.log('- 如果两种方法都成功 → 检查其他问题')

console.log('\n请执行测试并告诉我结果！')
