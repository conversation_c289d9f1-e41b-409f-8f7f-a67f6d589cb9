// 直接测试云函数
console.log('🧪 测试downloadResource云函数')

console.log('\n请在微信开发者工具控制台中执行以下代码:')

const testCode = `
console.log('🔧 测试downloadResource云函数...');

// 测试鞋子图片文件
wx.cloud.callFunction({
  name: 'downloadResource',
  data: {
    cloudPath: 'resources/images/wordlist/鞋子.png',
    type: 'image'
  },
  success: (res) => {
    console.log('📋 downloadResource云函数返回:');
    console.log('完整响应:', res);
    
    if (res.result) {
      console.log('result存在:', res.result);
      console.log('success:', res.result.success);
      
      if (res.result.success) {
        console.log('✅ 云函数成功');
        console.log('临时链接:', res.result.data.tempFileURL);
      } else {
        console.log('❌ 云函数返回失败');
        console.log('错误信息:', res.result.error);
      }
    } else {
      console.log('❌ 没有result字段');
    }
  },
  fail: (err) => {
    console.error('❌ downloadResource云函数调用失败:', err);
    console.log('错误详情:', JSON.stringify(err, null, 2));
  }
});

// 同时测试直接调用
console.log('\\n🔧 测试直接调用wx.cloud.getTempFileURL...');

wx.cloud.getTempFileURL({
  fileList: ['resources/images/wordlist/鞋子.png'],
  success: (res) => {
    console.log('📋 直接调用返回:');
    console.log('完整响应:', res);
    
    if (res.fileList && res.fileList.length > 0) {
      const fileInfo = res.fileList[0];
      if (fileInfo.tempFileURL) {
        console.log('✅ 直接调用成功');
        console.log('临时链接:', fileInfo.tempFileURL);
      } else {
        console.log('❌ 直接调用失败');
        console.log('错误信息:', fileInfo.errMsg);
      }
    }
  },
  fail: (err) => {
    console.error('❌ 直接调用失败:', err);
  }
});
`

console.log(testCode)

console.log('\n📝 预期结果分析:')
console.log('1. 如果云函数成功: 说明云函数正常，问题在代码逻辑')
console.log('2. 如果云函数失败但直接调用成功: 说明云函数有问题')
console.log('3. 如果都失败: 说明权限或环境问题')
console.log('4. 如果都成功: 说明是缓存问题')

console.log('\n请执行测试并告诉我结果！')
