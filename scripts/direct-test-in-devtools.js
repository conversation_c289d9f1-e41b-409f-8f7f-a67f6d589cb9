// 直接在微信开发者工具中测试
console.log('🧪 请在微信开发者工具控制台中执行以下代码进行诊断：')

const testCode = `
console.log('🔍 开始全面诊断...');

// 第一步：测试环境信息
console.log('\\n📋 步骤1: 检查环境信息');
console.log('当前环境:', wx.cloud.getEnvironment ? wx.cloud.getEnvironment() : '无法获取');

// 第二步：测试getResourceList云函数
console.log('\\n📋 步骤2: 测试getResourceList云函数');
wx.cloud.callFunction({
  name: 'getResourceList',
  data: {},
  success: (res) => {
    console.log('getResourceList完整响应:', res);
    
    if (res.result && res.result.success) {
      console.log('✅ getResourceList成功');
      const { audio, image } = res.result.data;
      console.log(\`资源统计: 音频\${audio.length}个, 图片\${image.length}个\`);
      
      // 查找冰箱相关文件
      const bingxiangAudio = audio.filter(item => item.word === '冰箱');
      const bingxiangImage = image.filter(item => item.word === '冰箱');
      
      console.log('\\n🧊 冰箱相关文件:');
      console.log('音频文件:', bingxiangAudio);
      console.log('图片文件:', bingxiangImage);
      
      // 第三步：测试downloadResource云函数
      if (bingxiangImage.length > 0) {
        const testFile = bingxiangImage[0];
        console.log(\`\\n📋 步骤3: 测试downloadResource云函数 - \${testFile.cloudPath}\`);
        
        wx.cloud.callFunction({
          name: 'downloadResource',
          data: {
            cloudPath: testFile.cloudPath,
            type: 'image'
          },
          success: (downloadRes) => {
            console.log('downloadResource完整响应:', downloadRes);
            
            if (downloadRes.result && downloadRes.result.success) {
              console.log('✅ downloadResource成功');
              console.log('临时链接:', downloadRes.result.data.tempFileURL);
            } else {
              console.log('❌ downloadResource失败');
              console.log('错误信息:', downloadRes.result?.error);
            }
          },
          fail: (err) => {
            console.log('❌ downloadResource调用失败:', err);
          }
        });
      }
      
      // 第四步：测试直接调用wx.cloud.getTempFileURL
      if (bingxiangImage.length > 0) {
        const testFile = bingxiangImage[0];
        console.log(\`\\n📋 步骤4: 测试直接调用 - \${testFile.cloudPath}\`);
        
        wx.cloud.getTempFileURL({
          fileList: [testFile.cloudPath],
          success: (tempRes) => {
            console.log('getTempFileURL完整响应:', tempRes);
            
            if (tempRes.fileList && tempRes.fileList.length > 0) {
              const fileInfo = tempRes.fileList[0];
              if (fileInfo.tempFileURL) {
                console.log('✅ 直接调用成功');
                console.log('临时链接:', fileInfo.tempFileURL);
              } else {
                console.log('❌ 直接调用失败');
                console.log('错误信息:', fileInfo.errMsg);
                console.log('错误代码:', fileInfo.errCode);
              }
            }
          },
          fail: (err) => {
            console.log('❌ 直接调用失败:', err);
          }
        });
      }
      
    } else {
      console.log('❌ getResourceList失败');
      console.log('错误信息:', res.result?.error);
    }
  },
  fail: (err) => {
    console.log('❌ getResourceList调用失败:', err);
  }
});

// 第五步：测试一些已知存在的文件路径
console.log('\\n📋 步骤5: 测试已知文件路径');
const knownFiles = [
  'resources/images/wordlist/冰箱.png',
  'resources/audio/female/冰箱.mp3',
  'resources/audio/male/冰箱.mp3'
];

knownFiles.forEach((cloudPath, index) => {
  console.log(\`\\n测试文件 \${index + 1}: \${cloudPath}\`);
  
  wx.cloud.getTempFileURL({
    fileList: [cloudPath],
    success: (res) => {
      const fileInfo = res.fileList[0];
      if (fileInfo.tempFileURL) {
        console.log(\`✅ 文件存在: \${cloudPath}\`);
      } else {
        console.log(\`❌ 文件不存在: \${cloudPath}\`);
        console.log(\`错误: \${fileInfo.errMsg}\`);
      }
    },
    fail: (err) => {
      console.log(\`❌ 调用失败: \${cloudPath}\`, err);
    }
  });
});

console.log('\\n🔍 诊断完成，请查看上述输出结果');
`

console.log(testCode)

console.log('\n📝 执行步骤:')
console.log('1. 复制上面的代码')
console.log('2. 在微信开发者工具控制台中粘贴')
console.log('3. 按回车执行')
console.log('4. 观察每个步骤的输出结果')

console.log('\n🎯 关键观察点:')
console.log('- getResourceList是否成功？')
console.log('- downloadResource是否成功？')
console.log('- 直接调用是否成功？')
console.log('- 具体的错误信息是什么？')

console.log('\n请执行测试并告诉我详细结果！')
