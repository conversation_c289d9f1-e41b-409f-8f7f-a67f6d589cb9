// 验证噪音测试分数显示修复的脚本
const fs = require('fs');
const path = require('path');

console.log('🔧 噪音测试分数显示修复验证');
console.log('='.repeat(50));

// 1. 检查测试历史页面的显示修复
console.log('\n📋 测试历史页面显示修复检查:');

try {
  const testHistoryWxmlPath = path.join(__dirname, '../miniprogram/pages/test-history/test-history.wxml');
  const testHistoryWxmlContent = fs.readFileSync(testHistoryWxmlPath, 'utf8');
  
  // 检查分数显示逻辑
  const scoreDisplayMatch = testHistoryWxmlContent.match(/\{\{item\.testType\s*===\s*['"]hearing_sai['"]\s*\?\s*item\.saiScore\s*:\s*item\.noiseScore\}\}/);
  if (scoreDisplayMatch) {
    console.log('✅ 分数显示逻辑已修复');
    console.log('   听音辨图: item.saiScore');
    console.log('   噪音测试: item.noiseScore');
  } else {
    console.log('❌ 分数显示逻辑未正确修复');
  }
  
  // 检查单位统一
  const unitMatch = testHistoryWxmlContent.match(/<text\s+class="score-unit">分<\/text>/);
  if (unitMatch) {
    console.log('✅ 单位已统一为"分"');
  } else {
    console.log('❌ 单位未统一');
  }
  
  // 检查等级评价逻辑
  const levelEvaluationMatch = testHistoryWxmlContent.match(/\{\{item\.noiseScore\s*>=\s*80\s*\?\s*['"]优秀['"]/);
  if (levelEvaluationMatch) {
    console.log('✅ 等级评价逻辑基于noiseScore');
  } else {
    console.log('❌ 等级评价逻辑未更新');
  }
  
} catch (error) {
  console.log('❌ 无法读取测试历史WXML文件');
}

// 2. 检查测试历史页面的数据处理修复
console.log('\n📊 测试历史页面数据处理修复检查:');

try {
  const testHistoryJsPath = path.join(__dirname, '../miniprogram/pages/test-history/test-history.js');
  const testHistoryJsContent = fs.readFileSync(testHistoryJsPath, 'utf8');
  
  // 检查noiseScore计算逻辑
  const noiseScoreMatch = testHistoryJsContent.match(/processedItem\.noiseScore\s*=\s*Math\.round\(accuracy\)/);
  if (noiseScoreMatch) {
    console.log('✅ noiseScore计算逻辑已添加');
    console.log('   计算方式: Math.round(accuracy)');
  } else {
    console.log('❌ noiseScore计算逻辑缺失');
  }
  
  // 检查调试日志
  const debugLogMatch = testHistoryJsContent.match(/转换后分数:\s*processedItem\.noiseScore/);
  if (debugLogMatch) {
    console.log('✅ 添加了转换后分数的调试日志');
  } else {
    console.log('❌ 缺少转换后分数的调试日志');
  }
  
} catch (error) {
  console.log('❌ 无法读取测试历史JS文件');
}

// 3. 检查test-result页面的显示修复
console.log('\n📄 test-result页面显示修复检查:');

try {
  const testResultWxmlPath = path.join(__dirname, '../miniprogram/pages/test-result/test-result.wxml');
  const testResultWxmlContent = fs.readFileSync(testResultWxmlPath, 'utf8');
  
  // 检查标签文本
  const labelMatch = testResultWxmlContent.match(/\{\{testType\s*===\s*['"]noise['"]\s*\?\s*['"]噪音测试得分['"]/);
  if (labelMatch) {
    console.log('✅ 标签已更新为"噪音测试得分"');
  } else {
    console.log('❌ 标签未更新');
  }
  
  // 检查分数显示
  const resultScoreMatch = testResultWxmlContent.match(/\{\{testType\s*===\s*['"]noise['"]\s*\?\s*noiseScore\s*:\s*saiScore\}\}/);
  if (resultScoreMatch) {
    console.log('✅ test-result页面分数显示已修复');
  } else {
    console.log('❌ test-result页面分数显示未修复');
  }
  
  // 检查单位统一
  const resultUnitMatch = testResultWxmlContent.match(/<text\s+class="score-unit">分<\/text>/);
  if (resultUnitMatch) {
    console.log('✅ test-result页面单位已统一为"分"');
  } else {
    console.log('❌ test-result页面单位未统一');
  }
  
} catch (error) {
  console.log('❌ 无法读取test-result WXML文件');
}

// 4. 检查test-result页面的数据处理修复
console.log('\n🔧 test-result页面数据处理修复检查:');

try {
  const testResultJsPath = path.join(__dirname, '../miniprogram/pages/test-result/test-result.js');
  const testResultJsContent = fs.readFileSync(testResultJsPath, 'utf8');
  
  // 检查noiseScore计算
  const resultNoiseScoreMatch = testResultJsContent.match(/const\s+noiseScore\s*=\s*Math\.round\(accuracyNum\)/);
  if (resultNoiseScoreMatch) {
    console.log('✅ test-result页面noiseScore计算已添加');
  } else {
    console.log('❌ test-result页面noiseScore计算缺失');
  }
  
  // 检查setData更新
  const setDataMatch = testResultJsContent.match(/noiseScore:\s*noiseScore/);
  if (setDataMatch) {
    console.log('✅ setData包含noiseScore字段');
  } else {
    console.log('❌ setData缺少noiseScore字段');
  }
  
} catch (error) {
  console.log('❌ 无法读取test-result JS文件');
}

// 5. 显示逻辑对比分析
console.log('\n🔍 显示逻辑对比分析:');

const displayComparison = {
  before: {
    historyPage: {
      score: 'item.overallAccuracy (可能为空)',
      unit: '% (百分比)',
      example: '% (只显示符号)'
    },
    resultPage: {
      score: 'overallAccuracy (百分比)',
      unit: '% (百分比)',
      example: '78.3%'
    }
  },
  after: {
    historyPage: {
      score: 'item.noiseScore (转换后的分数)',
      unit: '分 (统一单位)',
      example: '78分'
    },
    resultPage: {
      score: 'noiseScore (转换后的分数)',
      unit: '分 (统一单位)',
      example: '78分'
    }
  }
};

console.log('修复前:');
console.log('  测试历史页面:');
console.log(`    分数: ${displayComparison.before.historyPage.score}`);
console.log(`    单位: ${displayComparison.before.historyPage.unit}`);
console.log(`    示例: ${displayComparison.before.historyPage.example}`);
console.log('  测试结果页面:');
console.log(`    分数: ${displayComparison.before.resultPage.score}`);
console.log(`    单位: ${displayComparison.before.resultPage.unit}`);
console.log(`    示例: ${displayComparison.before.resultPage.example}`);

console.log('\n修复后:');
console.log('  测试历史页面:');
console.log(`    分数: ${displayComparison.after.historyPage.score}`);
console.log(`    单位: ${displayComparison.after.historyPage.unit}`);
console.log(`    示例: ${displayComparison.after.historyPage.example}`);
console.log('  测试结果页面:');
console.log(`    分数: ${displayComparison.after.resultPage.score}`);
console.log(`    单位: ${displayComparison.after.resultPage.unit}`);
console.log(`    示例: ${displayComparison.after.resultPage.example}`);

// 6. 转换逻辑说明
console.log('\n🔄 转换逻辑说明:');

const conversionLogic = [
  {
    input: '准确率 78.3%',
    process: 'Math.round(78.3)',
    output: '78分',
    note: '四舍五入为整数分数'
  },
  {
    input: '准确率 85.7%',
    process: 'Math.round(85.7)',
    output: '86分',
    note: '四舍五入为整数分数'
  },
  {
    input: '准确率 92.1%',
    process: 'Math.round(92.1)',
    output: '92分',
    note: '四舍五入为整数分数'
  }
];

conversionLogic.forEach((logic, index) => {
  console.log(`\n示例${index + 1}:`);
  console.log(`  输入: ${logic.input}`);
  console.log(`  处理: ${logic.process}`);
  console.log(`  输出: ${logic.output}`);
  console.log(`  说明: ${logic.note}`);
});

// 7. 等级评价标准
console.log('\n📊 等级评价标准:');

const gradeStandards = [
  { score: '80分及以上', grade: '优秀', color: 'green' },
  { score: '60-79分', grade: '良好', color: 'blue' },
  { score: '60分以下', grade: '需改进', color: 'orange' }
];

gradeStandards.forEach(standard => {
  console.log(`  ${standard.score}: ${standard.grade}`);
});

// 8. 一致性检查
console.log('\n✅ 一致性检查:');

const consistencyChecks = [
  {
    aspect: '单位统一',
    hearing: '分',
    noise: '分',
    status: '已统一'
  },
  {
    aspect: '分数范围',
    hearing: '0-100分',
    noise: '0-100分',
    status: '已统一'
  },
  {
    aspect: '等级评价',
    hearing: '基于saiScore',
    noise: '基于noiseScore',
    status: '逻辑一致'
  },
  {
    aspect: '显示格式',
    hearing: 'XX分',
    noise: 'XX分',
    status: '格式一致'
  }
];

consistencyChecks.forEach(check => {
  console.log(`\n🔧 ${check.aspect}:`);
  console.log(`  听音辨图: ${check.hearing}`);
  console.log(`  噪音测试: ${check.noise}`);
  console.log(`  状态: ${check.status}`);
});

// 9. 测试验证步骤
console.log('\n🧪 测试验证步骤:');

const testSteps = [
  '完成一次新的噪音测试（确保数据正确保存）',
  '进入测试历史页面',
  '检查噪音测试记录是否显示"XX分"而不是"%"',
  '验证分数是否为合理的整数（0-100）',
  '检查等级评价是否正确（优秀/良好/需改进）',
  '点击"查看详情"进入测试结果页面',
  '验证结果页面也显示"XX分"格式',
  '对比听音辨图和噪音测试的显示格式是否一致'
];

testSteps.forEach((step, index) => {
  console.log(`${index + 1}. ${step}`);
});

console.log('\n' + '='.repeat(50));
console.log('🎉 噪音测试分数显示修复验证完成！');

// 10. 修复总结
console.log('\n📊 修复总结:');
console.log('✅ 统一单位显示：噪音测试和听音辨图都显示"分"');
console.log('✅ 转换准确率为分数：Math.round(accuracy)');
console.log('✅ 更新等级评价逻辑：基于分数而不是百分比');
console.log('✅ 保持显示格式一致：两种测试都显示"XX分"');
console.log('✅ 修复空值显示问题：确保有具体的分数值');

console.log('\n🚀 现在噪音测试和听音辨图的显示格式完全一致了！');
