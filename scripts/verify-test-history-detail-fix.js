// 验证测试历史详情页面修复的脚本
const fs = require('fs');
const path = require('path');

console.log('📋 测试历史详情页面修复验证');
console.log('='.repeat(50));

// 1. 检查测试历史页面的viewDetail方法
console.log('\n🔍 测试历史页面viewDetail方法检查:');

try {
  const testHistoryJsPath = path.join(__dirname, '../miniprogram/pages/test-history/test-history.js');
  const testHistoryJsContent = fs.readFileSync(testHistoryJsPath, 'utf8');
  
  // 检查是否有类型判断逻辑
  const typeCheckMatch = testHistoryJsContent.match(/if\s*\(\s*testData\.testType\s*===\s*['"]noise_test['"]\s*\)/);
  if (typeCheckMatch) {
    console.log('✅ 测试类型判断逻辑存在');
    console.log('   判断条件: testData.testType === "noise_test"');
  } else {
    console.log('❌ 缺少测试类型判断逻辑');
  }
  
  // 检查是否有分别的处理方法
  const methodChecks = [
    { method: 'viewNoiseTestDetail', found: testHistoryJsContent.includes('viewNoiseTestDetail: function') },
    { method: 'viewHearingTestDetail', found: testHistoryJsContent.includes('viewHearingTestDetail: function') }
  ];
  
  methodChecks.forEach(check => {
    console.log(`${check.found ? '✅' : '❌'} ${check.method}方法: ${check.found ? '存在' : '缺失'}`);
  });
  
  // 检查是否使用filteredList
  const filteredListMatch = testHistoryJsContent.match(/this\.data\.filteredList\[index\]/);
  if (filteredListMatch) {
    console.log('✅ 正确使用filteredList获取测试数据');
  } else {
    console.log('❌ 未使用filteredList，可能导致过滤后的数据索引错误');
  }
  
} catch (error) {
  console.log('❌ 无法读取测试历史JS文件');
}

// 2. 检查听音辨图详情处理
console.log('\n🎧 听音辨图详情处理检查:');

try {
  const testHistoryJsPath = path.join(__dirname, '../miniprogram/pages/test-history/test-history.js');
  const testHistoryJsContent = fs.readFileSync(testHistoryJsPath, 'utf8');
  
  // 检查听音辨图数据处理
  const hearingDataMatch = testHistoryJsContent.match(/wx\.setStorageSync\(['"]testResults['"],\s*originalTestResults\)/);
  if (hearingDataMatch) {
    console.log('✅ 听音辨图数据通过本地存储传递');
  } else {
    console.log('❌ 听音辨图数据传递方式不正确');
  }
  
  // 检查跳转URL
  const hearingNavigateMatch = testHistoryJsContent.match(/wx\.navigateTo\s*\(\s*{\s*url:\s*['"]\/pages\/test-result\/test-result['"]/);
  if (hearingNavigateMatch) {
    console.log('✅ 听音辨图正确跳转到test-result页面');
  } else {
    console.log('❌ 听音辨图跳转URL不正确');
  }
  
} catch (error) {
  console.log('❌ 无法检查听音辨图详情处理');
}

// 3. 检查噪音测试详情处理
console.log('\n🔊 噪音测试详情处理检查:');

try {
  const testHistoryJsPath = path.join(__dirname, '../miniprogram/pages/test-history/test-history.js');
  const testHistoryJsContent = fs.readFileSync(testHistoryJsPath, 'utf8');
  
  // 检查噪音测试参数构造
  const noiseParamsCheck = [
    { param: 'type: "noise"', found: testHistoryJsContent.includes('type: \'noise\'') },
    { param: 'accuracy', found: testHistoryJsContent.includes('accuracy: testData.overallAccuracy') },
    { param: 'correct', found: testHistoryJsContent.includes('correct: testData.totalCorrect') },
    { param: 'total', found: testHistoryJsContent.includes('total: testData.totalTests') },
    { param: 'results', found: testHistoryJsContent.includes('results: JSON.stringify(testData.testResults') }
  ];
  
  console.log('噪音测试参数构造检查:');
  noiseParamsCheck.forEach(check => {
    console.log(`${check.found ? '✅' : '❌'} ${check.param}: ${check.found ? '正确' : '缺失'}`);
  });
  
  // 检查噪音测试跳转URL
  const noiseNavigateMatch = testHistoryJsContent.match(/wx\.navigateTo\s*\(\s*{\s*url:\s*`\/pages\/test-result\/test-result\?\$\{params\.toString\(\)\}`/);
  if (noiseNavigateMatch) {
    console.log('✅ 噪音测试正确跳转到test-result页面并传递参数');
  } else {
    console.log('❌ 噪音测试跳转URL不正确');
  }
  
} catch (error) {
  console.log('❌ 无法检查噪音测试详情处理');
}

// 4. 检查test-result页面的类型处理
console.log('\n📊 test-result页面类型处理检查:');

try {
  const testResultJsPath = path.join(__dirname, '../miniprogram/pages/test-result/test-result.js');
  const testResultJsContent = fs.readFileSync(testResultJsPath, 'utf8');
  
  // 检查类型判断
  const resultTypeCheckMatch = testResultJsContent.match(/if\s*\(\s*testType\s*===\s*['"]noise['"]\s*\)/);
  if (resultTypeCheckMatch) {
    console.log('✅ test-result页面有噪音测试类型判断');
  } else {
    console.log('❌ test-result页面缺少噪音测试类型判断');
  }
  
  // 检查噪音测试处理方法
  const noiseHandlerMatch = testResultJsContent.match(/handleNoiseTestResult:\s*function/);
  if (noiseHandlerMatch) {
    console.log('✅ test-result页面有噪音测试处理方法');
  } else {
    console.log('❌ test-result页面缺少噪音测试处理方法');
  }
  
  // 检查数据转换逻辑
  const dataConversionMatch = testResultJsContent.match(/recognitionRate:\s*accuracyNum\.toFixed\(1\)/);
  if (dataConversionMatch) {
    console.log('✅ test-result页面有数据转换逻辑');
  } else {
    console.log('❌ test-result页面缺少数据转换逻辑');
  }
  
} catch (error) {
  console.log('❌ 无法检查test-result页面');
}

// 5. 数据流程分析
console.log('\n🔄 数据流程分析:');

const dataFlows = {
  hearing: {
    name: '听音辨图测试',
    steps: [
      '1. 点击查看详情 → viewDetail方法',
      '2. 判断testType !== "noise_test" → viewHearingTestDetail',
      '3. 数据通过wx.setStorageSync存储',
      '4. 跳转到/pages/test-result/test-result',
      '5. test-result页面从本地存储读取数据',
      '6. 直接使用recognitionRate字段显示'
    ]
  },
  noise: {
    name: '噪音测试',
    steps: [
      '1. 点击查看详情 → viewDetail方法',
      '2. 判断testType === "noise_test" → viewNoiseTestDetail',
      '3. 构造URL参数(type=noise, accuracy, correct, total, results)',
      '4. 跳转到/pages/test-result/test-result?参数',
      '5. test-result页面检测type=noise → handleNoiseTestResult',
      '6. 转换accuracy为recognitionRate格式显示'
    ]
  }
};

Object.values(dataFlows).forEach(flow => {
  console.log(`\n📋 ${flow.name}数据流程:`);
  flow.steps.forEach(step => {
    console.log(`  ${step}`);
  });
});

// 6. 关键差异对比
console.log('\n🔍 关键差异对比:');

const keyDifferences = [
  {
    aspect: '数据传递方式',
    hearing: '本地存储 (wx.setStorageSync)',
    noise: 'URL参数 (URLSearchParams)'
  },
  {
    aspect: '跳转URL',
    hearing: '/pages/test-result/test-result',
    noise: '/pages/test-result/test-result?type=noise&...'
  },
  {
    aspect: '数据字段',
    hearing: 'recognitionRate (直接使用)',
    noise: 'accuracy → recognitionRate (需转换)'
  },
  {
    aspect: '处理方法',
    hearing: '从本地存储读取',
    noise: 'handleNoiseTestResult方法'
  }
];

keyDifferences.forEach(diff => {
  console.log(`\n📊 ${diff.aspect}:`);
  console.log(`  听音辨图: ${diff.hearing}`);
  console.log(`  噪音测试: ${diff.noise}`);
});

// 7. 潜在问题检查
console.log('\n⚠️ 潜在问题检查:');

const potentialIssues = [
  {
    issue: '过滤后的索引问题',
    description: '使用filteredList而不是historyList获取数据',
    status: '已修复'
  },
  {
    issue: '数据结构不兼容',
    description: '噪音测试使用accuracy，test-result期望recognitionRate',
    status: '已修复'
  },
  {
    issue: '类型判断缺失',
    description: '原来没有根据testType进行不同处理',
    status: '已修复'
  },
  {
    issue: 'URL参数长度限制',
    description: '噪音测试数据可能导致URL过长',
    status: '需要监控'
  }
];

potentialIssues.forEach(issue => {
  console.log(`\n🔧 ${issue.issue}:`);
  console.log(`  描述: ${issue.description}`);
  console.log(`  状态: ${issue.status}`);
});

// 8. 测试验证步骤
console.log('\n🧪 测试验证步骤:');

const testSteps = [
  '完成一次听音辨图测试和一次噪音测试',
  '进入测试记录页面，确认两种测试都有记录',
  '点击听音辨图记录的"查看详情"按钮',
  '验证跳转到听音辨图测试报告页面',
  '返回测试记录页面',
  '点击噪音测试记录的"查看详情"按钮',
  '验证跳转到噪音测试报告页面',
  '检查两个报告页面的标题和内容是否正确',
  '使用过滤器分别查看两种测试类型',
  '验证过滤后的查看详情功能是否正常'
];

testSteps.forEach((step, index) => {
  console.log(`${index + 1}. ${step}`);
});

console.log('\n' + '='.repeat(50));
console.log('🎉 测试历史详情页面修复验证完成！');

// 9. 修复总结
console.log('\n📊 修复总结:');
console.log('✅ 添加测试类型判断逻辑');
console.log('✅ 分离听音辨图和噪音测试的处理方法');
console.log('✅ 修复过滤后的数据索引问题');
console.log('✅ 确保数据传递方式的正确性');
console.log('✅ 保持与现有逻辑的兼容性');

console.log('\n🚀 现在测试记录的查看详情功能应该正确区分测试类型了！');
