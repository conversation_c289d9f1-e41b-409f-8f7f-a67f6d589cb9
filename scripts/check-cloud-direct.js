// 直接检查云存储状态的脚本
const cloud = require('wx-server-sdk')

// 初始化云开发
cloud.init({
  env: 'cloud1-0gjev5gfdef4d262'
})

async function checkCloudStorage() {
  try {
    console.log('🔍 开始检查云存储状态...')
    
    // 1. 测试getResourceList云函数
    console.log('\n📋 测试getResourceList云函数...')
    try {
      const getResourceListResult = await cloud.callFunction({
        name: 'getResourceList',
        data: {}
      })
      
      console.log('getResourceList调用成功:', getResourceListResult.result.success)
      
      if (getResourceListResult.result.success) {
        const { audio, image } = getResourceListResult.result.data
        console.log(`📊 资源统计: 音频${audio.length}个, 图片${image.length}个`)
        
        // 显示前几个文件路径
        console.log('\n📁 前3个音频文件路径:')
        audio.slice(0, 3).forEach((item, index) => {
          console.log(`${index + 1}. ${item.word} (${item.gender}): ${item.cloudPath}`)
        })
        
        console.log('\n🖼️ 前3个图片文件路径:')
        image.slice(0, 3).forEach((item, index) => {
          console.log(`${index + 1}. ${item.word}: ${item.cloudPath}`)
        })
        
        // 2. 测试几个关键文件是否存在
        console.log('\n🔍 检查关键文件是否存在...')
        const testFiles = [
          audio[0]?.cloudPath, // 第一个音频文件
          audio[audio.length - 1]?.cloudPath, // 最后一个音频文件
          image[0]?.cloudPath, // 第一个图片文件
          'resources/audio/male/窗户.mp3', // 错误日志中的文件
          'resources/audio/male/自行车.mp3',
          'resources/audio/male/花朵.mp3'
        ].filter(Boolean)
        
        for (const cloudPath of testFiles) {
          try {
            console.log(`检查文件: ${cloudPath}`)
            
            const tempFileResult = await cloud.getTempFileURL({
              fileList: [cloudPath]
            })
            
            const fileInfo = tempFileResult.fileList[0]
            if (fileInfo.tempFileURL) {
              console.log(`✅ 文件存在: ${cloudPath}`)
            } else {
              console.log(`❌ 文件不存在: ${cloudPath}`)
              console.log(`   错误信息: ${fileInfo.errMsg}`)
            }
          } catch (error) {
            console.log(`❌ 检查文件失败: ${cloudPath}`, error.message)
          }
        }
        
        // 3. 测试downloadResource云函数
        console.log('\n🔧 测试downloadResource云函数...')
        try {
          const downloadResult = await cloud.callFunction({
            name: 'downloadResource',
            data: {
              cloudPath: image[0]?.cloudPath || 'resources/images/wordlist/书本.png',
              type: 'image'
            }
          })
          
          console.log('downloadResource调用成功:', downloadResult.result.success)
          if (downloadResult.result.success) {
            console.log('✅ downloadResource云函数正常工作')
            console.log('临时链接:', downloadResult.result.data.tempFileURL)
          } else {
            console.log('❌ downloadResource返回失败:', downloadResult.result.error)
          }
        } catch (error) {
          console.log('❌ downloadResource云函数调用失败:', error.message)
        }
        
      } else {
        console.log('❌ getResourceList返回失败:', getResourceListResult.result.error)
      }
    } catch (error) {
      console.log('❌ getResourceList云函数调用失败:', error.message)
    }
    
    // 4. 列出云存储中的实际文件
    console.log('\n📁 列出云存储中的实际文件...')
    try {
      const storage = cloud.storage()
      
      // 列出resources目录下的文件
      console.log('检查resources目录...')
      const resourceFiles = await storage.listFile({
        prefix: 'resources/',
        maxKeys: 20
      })
      
      console.log(`找到 ${resourceFiles.fileList.length} 个文件:`)
      resourceFiles.fileList.forEach((file, index) => {
        console.log(`${index + 1}. ${file.fileID}`)
      })
      
      if (resourceFiles.fileList.length === 0) {
        console.log('⚠️ resources目录下没有找到任何文件！')
      }
      
    } catch (error) {
      console.log('❌ 列出文件失败:', error.message)
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error)
  }
}

// 运行检查
checkCloudStorage()
