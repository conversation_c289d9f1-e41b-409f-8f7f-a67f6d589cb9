// 调试文件访问问题
console.log('🔍 调试云存储文件访问问题')

console.log('\n📋 可能的问题原因:')

console.log('\n1. 文件路径格式问题')
console.log('   - 期望路径: resources/audio/male/窗户.mp3')
console.log('   - 实际路径可能不同')

console.log('\n2. 文件ID vs 路径问题')
console.log('   - 云存储可能使用 fileID 格式')
console.log('   - 例如: cloud://env-xxx.xxx/resources/audio/male/窗户.mp3')

console.log('\n3. 权限问题')
console.log('   - 小程序端权限不足')
console.log('   - 需要通过云函数访问')

console.log('\n4. 环境问题')
console.log('   - 文件上传到了错误的环境')
console.log('   - 当前环境: cloud1-0gjev5gfdef4d262')

console.log('\n🧪 在微信开发者工具中测试以下代码:')

console.log('\n=== 测试1: 检查不同路径格式 ===')
const testCode1 = `
const testPaths = [
  'resources/audio/male/窗户.mp3',
  'cloud://cloud1-0gjev5gfdef4d262.636c-cloud1-0gjev5gfdef4d262-1330046817/resources/audio/male/窗户.mp3',
  '/resources/audio/male/窗户.mp3',
  'audio/male/窗户.mp3',
  'resources/audio/male/窗户.mp3'
];

console.log('🔍 测试不同路径格式...');

testPaths.forEach((path, index) => {
  wx.cloud.getTempFileURL({
    fileList: [path],
    success: (res) => {
      const fileInfo = res.fileList[0];
      console.log(\`\${index + 1}. 路径: \${path}\`);
      if (fileInfo.tempFileURL) {
        console.log('✅ 成功获取临时链接');
      } else {
        console.log('❌ 失败:', fileInfo.errMsg);
      }
      console.log('---');
    },
    fail: (err) => {
      console.log(\`\${index + 1}. 路径: \${path} - 调用失败:\`, err);
    }
  });
});`

console.log(testCode1)

console.log('\n=== 测试2: 检查环境信息 ===')
const testCode2 = `
console.log('🔧 检查环境信息...');
console.log('当前环境ID:', wx.cloud.getEnvironment ? wx.cloud.getEnvironment() : '无法获取');

// 检查云开发初始化状态
try {
  const cloudInfo = wx.cloud;
  console.log('云开发对象存在:', !!cloudInfo);
} catch (error) {
  console.log('云开发未初始化:', error);
}`

console.log(testCode2)

console.log('\n=== 测试3: 通过云函数检查文件 ===')
const testCode3 = `
console.log('☁️ 通过云函数检查文件...');

// 先测试getResourceList
wx.cloud.callFunction({
  name: 'getResourceList',
  data: {},
  success: (res) => {
    console.log('getResourceList结果:', res.result);
    
    if (res.result.success) {
      const { audio, image } = res.result.data;
      console.log(\`资源统计: 音频\${audio.length}个, 图片\${image.length}个\`);
      
      // 找到窗户相关的文件
      const windowFiles = audio.filter(item => item.word === '窗户');
      console.log('窗户相关文件:', windowFiles);
      
      if (windowFiles.length > 0) {
        // 测试downloadResource云函数
        const testFile = windowFiles[0];
        console.log('测试文件:', testFile);
        
        wx.cloud.callFunction({
          name: 'downloadResource',
          data: {
            cloudPath: testFile.cloudPath,
            type: 'audio'
          },
          success: (downloadRes) => {
            console.log('downloadResource结果:', downloadRes.result);
            if (downloadRes.result.success) {
              console.log('✅ 通过云函数成功获取文件');
              console.log('临时链接:', downloadRes.result.data.tempFileURL);
            } else {
              console.log('❌ 云函数也无法获取文件:', downloadRes.result.error);
            }
          },
          fail: (err) => {
            console.log('❌ downloadResource调用失败:', err);
          }
        });
      }
    }
  },
  fail: (err) => {
    console.log('❌ getResourceList调用失败:', err);
  }
});`

console.log(testCode3)

console.log('\n=== 测试4: 检查文件上传记录 ===')
const testCode4 = `
console.log('📁 检查可能的文件路径...');

// 测试可能的文件路径变体
const possiblePaths = [
  'resources/audio/male/窗户.mp3',
  'resources/audio/男声/窗户.mp3',
  'audio/male/窗户.mp3',
  'audio/男声/窗户.mp3',
  'resources/窗户.mp3',
  'male/窗户.mp3'
];

possiblePaths.forEach((path, index) => {
  wx.cloud.getTempFileURL({
    fileList: [path],
    success: (res) => {
      const fileInfo = res.fileList[0];
      console.log(\`\${index + 1}. \${path}: \${fileInfo.tempFileURL ? '✅存在' : '❌不存在'}\`);
    }
  });
});`

console.log(testCode4)

console.log('\n📝 执行步骤:')
console.log('1. 在微信开发者工具中依次执行上述测试代码')
console.log('2. 观察哪个路径格式能成功获取文件')
console.log('3. 检查getResourceList返回的实际路径')
console.log('4. 验证downloadResource云函数是否正常')

console.log('\n💡 预期结果:')
console.log('- 如果某个路径格式成功: 说明文件存在，只是路径格式问题')
console.log('- 如果云函数能获取: 说明权限问题，需要统一使用云函数')
console.log('- 如果都失败: 说明文件确实不存在，需要重新上传')

console.log('\n请执行测试并告诉我结果！')
