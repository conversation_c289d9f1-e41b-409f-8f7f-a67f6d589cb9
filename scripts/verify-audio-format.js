#!/usr/bin/env node

/**
 * 验证音频文件格式和小程序兼容性
 * 检查音频文件是否符合小程序要求
 */

const fs = require('fs');
const path = require('path');

// 小程序支持的音频格式
const SUPPORTED_FORMATS = ['.mp3', '.m4a', '.aac', '.wav'];

// 验证音频文件格式
function verifyAudioFormat() {
  console.log('🔍 验证音频文件格式和小程序兼容性...\n');
  
  const audioDir = path.join(__dirname, '..', 'miniprogram', 'audio');
  const levels = ['55db', '70db', '85db'];
  
  let totalFiles = 0;
  let validFiles = 0;
  let invalidFiles = 0;
  const issues = [];
  
  levels.forEach(level => {
    const levelDir = path.join(audioDir, level);
    
    if (!fs.existsSync(levelDir)) {
      issues.push(`❌ 目录不存在: ${level}`);
      return;
    }
    
    console.log(`📂 检查 ${level} 目录...`);
    
    const files = fs.readdirSync(levelDir);
    
    files.forEach(file => {
      totalFiles++;
      const filePath = path.join(levelDir, file);
      const ext = path.extname(file).toLowerCase();
      
      // 检查文件扩展名
      if (!SUPPORTED_FORMATS.includes(ext)) {
        invalidFiles++;
        issues.push(`❌ 不支持的格式: ${level}/${file} (${ext})`);
        return;
      }
      
      // 检查文件大小
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      
      if (stats.size === 0) {
        invalidFiles++;
        issues.push(`❌ 空文件: ${level}/${file}`);
        return;
      }
      
      if (stats.size > 10 * 1024 * 1024) { // 10MB
        issues.push(`⚠️  文件过大: ${level}/${file} (${sizeKB}KB)`);
      }
      
      validFiles++;
      console.log(`  ✅ ${file} - ${sizeKB}KB`);
    });
  });
  
  console.log('\n=== 验证结果 ===');
  console.log(`📁 总文件数: ${totalFiles}`);
  console.log(`✅ 有效文件: ${validFiles}`);
  console.log(`❌ 无效文件: ${invalidFiles}`);
  console.log(`📈 有效率: ${totalFiles > 0 ? ((validFiles / totalFiles) * 100).toFixed(1) : 0}%`);
  
  if (issues.length > 0) {
    console.log('\n⚠️  发现的问题:');
    issues.forEach(issue => console.log(`   ${issue}`));
  } else {
    console.log('\n🎉 所有音频文件格式验证通过！');
  }
  
  return {
    totalFiles,
    validFiles,
    invalidFiles,
    issues
  };
}

// 生成音频文件清单
function generateAudioManifest() {
  console.log('\n📋 生成音频文件清单...');
  
  const audioDir = path.join(__dirname, '..', 'miniprogram', 'audio');
  const levels = ['55db', '70db', '85db'];
  const manifest = {
    generatedAt: new Date().toISOString(),
    totalFiles: 0,
    levels: {}
  };
  
  levels.forEach(level => {
    const levelDir = path.join(audioDir, level);
    manifest.levels[level] = {
      files: [],
      count: 0,
      totalSize: 0
    };
    
    if (fs.existsSync(levelDir)) {
      const files = fs.readdirSync(levelDir);
      
      files.forEach(file => {
        const filePath = path.join(levelDir, file);
        const stats = fs.statSync(filePath);
        const fileInfo = {
          name: file,
          word: path.basename(file, path.extname(file)),
          size: stats.size,
          sizeKB: (stats.size / 1024).toFixed(2),
          format: path.extname(file).toLowerCase(),
          path: `/audio/${level}/${file}`
        };
        
        manifest.levels[level].files.push(fileInfo);
        manifest.levels[level].totalSize += stats.size;
        manifest.totalFiles++;
      });
      
      manifest.levels[level].count = files.length;
    }
  });
  
  // 保存清单文件
  const manifestPath = path.join(__dirname, '..', 'miniprogram', 'config', 'audio-manifest.json');
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
  
  console.log(`✅ 音频文件清单已保存到: ${manifestPath}`);
  console.log(`📊 总计 ${manifest.totalFiles} 个音频文件`);
  
  return manifest;
}

// 检查音频文件命名规范
function checkNamingConvention() {
  console.log('\n🏷️  检查文件命名规范...');
  
  const audioDir = path.join(__dirname, '..', 'miniprogram', 'audio');
  const levels = ['55db', '70db', '85db'];
  const namingIssues = [];
  
  levels.forEach(level => {
    const levelDir = path.join(audioDir, level);
    
    if (fs.existsSync(levelDir)) {
      const files = fs.readdirSync(levelDir);
      
      files.forEach(file => {
        const baseName = path.basename(file, path.extname(file));
        
        // 检查是否包含中文字符
        if (!/[\u4e00-\u9fa5]/.test(baseName)) {
          namingIssues.push(`⚠️  非中文词汇: ${level}/${file}`);
        }
        
        // 检查是否包含特殊字符
        if (/[^\u4e00-\u9fa5a-zA-Z0-9]/.test(baseName)) {
          namingIssues.push(`⚠️  包含特殊字符: ${level}/${file}`);
        }
        
        // 检查长度
        if (baseName.length > 10) {
          namingIssues.push(`⚠️  文件名过长: ${level}/${file}`);
        }
      });
    }
  });
  
  if (namingIssues.length > 0) {
    console.log('发现命名问题:');
    namingIssues.forEach(issue => console.log(`   ${issue}`));
  } else {
    console.log('✅ 文件命名规范检查通过');
  }
  
  return namingIssues;
}

// 主函数
function main() {
  console.log('🎵 音频文件格式验证工具\n');
  
  try {
    // 验证音频格式
    const formatResult = verifyAudioFormat();
    
    // 检查命名规范
    const namingIssues = checkNamingConvention();
    
    // 生成清单
    const manifest = generateAudioManifest();
    
    console.log('\n🏁 验证完成！');
    
    if (formatResult.invalidFiles === 0 && namingIssues.length === 0) {
      console.log('✅ 所有检查通过，音频文件已准备就绪！');
      
      console.log('\n📋 小程序使用建议:');
      console.log('   1. 使用 wx.createInnerAudioContext() 播放音频');
      console.log('   2. 设置正确的事件监听器 (onPlay, onEnded, onError)');
      console.log('   3. 记得在页面卸载时销毁音频上下文');
      console.log('   4. 处理音频播放失败的降级方案');
      
      process.exit(0);
    } else {
      console.log('⚠️  发现问题，请检查并修复');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 运行验证
if (require.main === module) {
  main();
}

module.exports = {
  verifyAudioFormat,
  generateAudioManifest,
  checkNamingConvention
};
