// 测试优化后的下载功能
const cloud = require('wx-server-sdk')

// 设置环境变量
process.env.TENCENT_SECRET_ID = "AKIDpZ8YpzxvyueFeqr7C4jBRUp24tZ3NVUP"
process.env.TENCENT_SECRET_KEY = "ALQK1jPwIVaUBj8NckjX8XpywiLo58IK"

cloud.init({
  env: 'cloud1-0gjev5gfdef4d262'
})

async function testOptimizedDownload() {
  try {
    console.log('🔍 测试优化后的下载功能...')
    
    // 测试1: 验证 getResourceList 返回正确的 fileID 格式
    console.log('\n📋 测试1: getResourceList 云函数')
    const getResourceListResult = await cloud.callFunction({
      name: 'getResourceList',
      data: {}
    })
    
    if (getResourceListResult.result.success) {
      const { audio, image } = getResourceListResult.result.data
      console.log(`✅ getResourceList 成功: 音频${audio.length}个, 图片${image.length}个`)
      
      // 验证 fileID 格式
      const sampleImage = image[0]
      const sampleAudio = audio[0]
      
      console.log('图片 fileID 格式:', sampleImage.cloudPath.startsWith('cloud://') ? '✅正确' : '❌错误')
      console.log('音频 fileID 格式:', sampleAudio.cloudPath.startsWith('cloud://') ? '✅正确' : '❌错误')
      
      // 测试2: 验证 downloadResource 云函数
      console.log('\n📋 测试2: downloadResource 云函数')
      
      const testFiles = [
        { name: '冰箱图片', file: image.find(f => f.word === '冰箱'), type: 'image' },
        { name: '书本女声', file: audio.find(f => f.word === '书本' && f.gender === 'female'), type: 'audio' },
        { name: '窗户男声', file: audio.find(f => f.word === '窗户' && f.gender === 'male'), type: 'audio' }
      ]
      
      for (const test of testFiles) {
        if (test.file) {
          console.log(`\n测试 ${test.name}: ${test.file.cloudPath}`)
          
          try {
            const downloadResult = await cloud.callFunction({
              name: 'downloadResource',
              data: {
                cloudPath: test.file.cloudPath,
                type: test.type
              }
            })
            
            if (downloadResult.result.success) {
              console.log(`✅ ${test.name} 下载成功`)
              console.log(`临时链接: ${downloadResult.result.data.tempFileURL}`)
              console.log(`有效期: ${downloadResult.result.data.maxAge}秒`)
            } else {
              console.log(`❌ ${test.name} 下载失败: ${downloadResult.result.error}`)
            }
          } catch (error) {
            console.log(`❌ ${test.name} 测试异常: ${error.message}`)
          }
        }
      }
      
      // 测试3: 验证临时链接可访问性
      console.log('\n📋 测试3: 临时链接可访问性')
      
      const testImage = image.find(f => f.word === '冰箱')
      if (testImage) {
        try {
          const downloadResult = await cloud.callFunction({
            name: 'downloadResource',
            data: {
              cloudPath: testImage.cloudPath,
              type: 'image'
            }
          })
          
          if (downloadResult.result.success) {
            const tempURL = downloadResult.result.data.tempFileURL
            
            // 使用 Node.js 的 https 模块测试链接
            const https = require('https')
            const url = require('url')
            
            const parsedUrl = url.parse(tempURL)
            
            const testRequest = new Promise((resolve, reject) => {
              const req = https.request({
                hostname: parsedUrl.hostname,
                path: parsedUrl.path,
                method: 'HEAD'
              }, (res) => {
                resolve({
                  statusCode: res.statusCode,
                  contentLength: res.headers['content-length'],
                  contentType: res.headers['content-type']
                })
              })
              
              req.on('error', reject)
              req.setTimeout(5000, () => {
                req.destroy()
                reject(new Error('请求超时'))
              })
              req.end()
            })
            
            const response = await testRequest
            
            if (response.statusCode === 200) {
              console.log('✅ 临时链接可访问')
              console.log(`文件大小: ${response.contentLength} 字节`)
              console.log(`文件类型: ${response.contentType}`)
            } else {
              console.log(`❌ 临时链接不可访问: HTTP ${response.statusCode}`)
            }
          }
        } catch (error) {
          console.log(`❌ 临时链接测试失败: ${error.message}`)
        }
      }
      
    } else {
      console.log('❌ getResourceList 失败:', getResourceListResult.result.error)
    }
    
    console.log('\n🎯 优化后的下载功能测试完成!')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
  }
}

// 运行测试
testOptimizedDownload()
