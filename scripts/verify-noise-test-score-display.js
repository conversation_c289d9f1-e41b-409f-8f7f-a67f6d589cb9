// 验证噪音测试分数显示的脚本
const fs = require('fs');
const path = require('path');

console.log('📊 噪音测试分数显示验证');
console.log('='.repeat(50));

// 1. 检查噪音测试数据保存
console.log('\n💾 噪音测试数据保存检查:');

try {
  const noiseTestJsPath = path.join(__dirname, '../miniprogram/pages/noise-test/noise-test.js');
  const noiseTestJsContent = fs.readFileSync(noiseTestJsPath, 'utf8');
  
  // 检查overallAccuracy计算
  const accuracyCalcMatch = noiseTestJsContent.match(/overallAccuracy\s*=\s*\([^)]+\)\.toFixed\(1\)/);
  if (accuracyCalcMatch) {
    console.log('✅ overallAccuracy计算逻辑存在');
    console.log(`   计算方式: ${accuracyCalcMatch[0]}`);
  } else {
    console.log('❌ 未找到overallAccuracy计算逻辑');
  }
  
  // 检查云函数调用中的数据字段
  const cloudFunctionMatch = noiseTestJsContent.match(/wx\.cloud\.callFunction\s*\(\s*{[\s\S]*?overallAccuracy:\s*parseFloat\(overallAccuracy\)[\s\S]*?}\s*\)/);
  if (cloudFunctionMatch) {
    console.log('✅ 云函数调用包含overallAccuracy字段');
  } else {
    console.log('❌ 云函数调用缺少overallAccuracy字段');
  }
  
  // 检查testType设置
  const testTypeMatch = noiseTestJsContent.match(/testType:\s*['"]noise_test['"]/);
  if (testTypeMatch) {
    console.log('✅ testType设置为noise_test');
  } else {
    console.log('❌ testType设置不正确');
  }
  
} catch (error) {
  console.log('❌ 无法读取噪音测试JS文件');
}

// 2. 检查云函数数据返回
console.log('\n☁️ 云函数数据返回检查:');

try {
  const getTestHistoryPath = path.join(__dirname, '../cloudfunctions/getTestHistory/index.js');
  const getTestHistoryContent = fs.readFileSync(getTestHistoryPath, 'utf8');
  
  // 检查返回数据中是否包含噪音测试字段
  const noiseFieldsCheck = [
    { field: 'overallAccuracy', found: getTestHistoryContent.includes('overallAccuracy: item.overallAccuracy') },
    { field: 'totalCorrect', found: getTestHistoryContent.includes('totalCorrect: item.totalCorrect') },
    { field: 'totalTests', found: getTestHistoryContent.includes('totalTests: item.totalTests') }
  ];
  
  noiseFieldsCheck.forEach(check => {
    console.log(`${check.found ? '✅' : '❌'} ${check.field}字段: ${check.found ? '已包含' : '缺失'}`);
  });
  
  // 检查听音辨图字段是否保留
  const hearingFieldsCheck = [
    { field: 'saiScore', found: getTestHistoryContent.includes('saiScore: item.saiScore') },
    { field: 'saiLevel', found: getTestHistoryContent.includes('saiLevel: item.saiLevel') },
    { field: 'saiLevelClass', found: getTestHistoryContent.includes('saiLevelClass: item.saiLevelClass') }
  ];
  
  console.log('\n听音辨图字段保留检查:');
  hearingFieldsCheck.forEach(check => {
    console.log(`${check.found ? '✅' : '❌'} ${check.field}字段: ${check.found ? '已保留' : '缺失'}`);
  });
  
} catch (error) {
  console.log('❌ 无法读取云函数文件');
}

// 3. 检查测试历史页面数据处理
console.log('\n📋 测试历史页面数据处理检查:');

try {
  const testHistoryJsPath = path.join(__dirname, '../miniprogram/pages/test-history/test-history.js');
  const testHistoryJsContent = fs.readFileSync(testHistoryJsPath, 'utf8');
  
  // 检查噪音测试数据处理
  const noiseDataProcessMatch = testHistoryJsContent.match(/if\s*\(\s*item\.testType\s*===\s*['"]noise_test['"]\s*&&\s*item\.overallAccuracy/);
  if (noiseDataProcessMatch) {
    console.log('✅ 噪音测试数据特殊处理逻辑存在');
  } else {
    console.log('❌ 缺少噪音测试数据特殊处理逻辑');
  }
  
  // 检查overallAccuracy类型转换
  const accuracyTypeConversionMatch = testHistoryJsContent.match(/parseFloat\(item\.overallAccuracy\)/);
  if (accuracyTypeConversionMatch) {
    console.log('✅ overallAccuracy类型转换逻辑存在');
  } else {
    console.log('❌ 缺少overallAccuracy类型转换逻辑');
  }
  
} catch (error) {
  console.log('❌ 无法读取测试历史JS文件');
}

// 4. 检查WXML显示逻辑
console.log('\n📄 WXML显示逻辑检查:');

try {
  const testHistoryWxmlPath = path.join(__dirname, '../miniprogram/pages/test-history/test-history.wxml');
  const testHistoryWxmlContent = fs.readFileSync(testHistoryWxmlPath, 'utf8');
  
  // 检查分数显示逻辑
  const scoreDisplayMatch = testHistoryWxmlContent.match(/\{\{item\.testType\s*===\s*['"]hearing_sai['"]\s*\?\s*item\.saiScore\s*:\s*item\.overallAccuracy\}\}/);
  if (scoreDisplayMatch) {
    console.log('✅ 分数显示逻辑正确');
    console.log(`   逻辑: ${scoreDisplayMatch[0]}`);
  } else {
    console.log('❌ 分数显示逻辑不正确');
  }
  
  // 检查单位显示逻辑
  const unitDisplayMatch = testHistoryWxmlContent.match(/\{\{item\.testType\s*===\s*['"]hearing_sai['"]\s*\?\s*['"]分['"]\s*:\s*['"]%['"]\}\}/);
  if (unitDisplayMatch) {
    console.log('✅ 单位显示逻辑正确');
    console.log(`   逻辑: ${unitDisplayMatch[0]}`);
  } else {
    console.log('❌ 单位显示逻辑不正确');
  }
  
  // 检查等级显示逻辑
  const levelDisplayMatch = testHistoryWxmlContent.match(/\{\{item\.overallAccuracy\s*>=\s*80\s*\?\s*['"]优秀['"]/);
  if (levelDisplayMatch) {
    console.log('✅ 噪音测试等级显示逻辑存在');
  } else {
    console.log('❌ 噪音测试等级显示逻辑缺失');
  }
  
} catch (error) {
  console.log('❌ 无法读取测试历史WXML文件');
}

// 5. 数据流程分析
console.log('\n🔄 数据流程分析:');

const dataFlow = [
  {
    step: 1,
    stage: '噪音测试完成',
    action: '计算overallAccuracy = (totalCorrect / totalTests * 100).toFixed(1)',
    expected: '准确率百分比，保留1位小数'
  },
  {
    step: 2,
    stage: '数据保存',
    action: '调用saveTestResult云函数，传入overallAccuracy: parseFloat(overallAccuracy)',
    expected: '数字类型的准确率存储到数据库'
  },
  {
    step: 3,
    stage: '数据查询',
    action: 'getTestHistory云函数返回overallAccuracy字段',
    expected: '包含噪音测试的准确率数据'
  },
  {
    step: 4,
    stage: '数据处理',
    action: '测试历史页面确保overallAccuracy为数字类型',
    expected: '正确的数字格式用于显示'
  },
  {
    step: 5,
    stage: '界面显示',
    action: 'WXML根据testType显示对应的分数和单位',
    expected: '噪音测试显示准确率%，听音辨图显示SAI分'
  }
];

dataFlow.forEach(flow => {
  console.log(`\n步骤${flow.step}: ${flow.stage}`);
  console.log(`  操作: ${flow.action}`);
  console.log(`  预期: ${flow.expected}`);
});

// 6. 问题排查清单
console.log('\n🔍 问题排查清单:');

const troubleshootingChecklist = [
  '✅ 噪音测试是否正确计算overallAccuracy',
  '✅ 噪音测试是否正确保存数据到云函数',
  '✅ 云函数是否返回噪音测试的overallAccuracy字段',
  '✅ 测试历史页面是否正确处理噪音测试数据',
  '✅ WXML是否根据testType正确显示分数',
  '⚠️ 数据库中是否存在噪音测试记录',
  '⚠️ 现有噪音测试记录的数据格式是否正确'
];

troubleshootingChecklist.forEach(item => {
  console.log(item);
});

// 7. 修复总结
console.log('\n🔧 修复总结:');

const fixSummary = [
  '修复云函数getTestHistory，添加噪音测试字段返回',
  '增强测试历史页面数据处理，确保overallAccuracy类型正确',
  '验证WXML显示逻辑，确保根据testType显示正确内容',
  '确保噪音测试数据保存包含所有必要字段',
  '添加数据类型转换，防止字符串/数字类型问题'
];

fixSummary.forEach((fix, index) => {
  console.log(`${index + 1}. ${fix}`);
});

// 8. 测试建议
console.log('\n🧪 测试建议:');

const testSuggestions = [
  '完成一次新的噪音测试，检查数据是否正确保存',
  '在测试历史页面查看噪音测试记录是否显示分数',
  '验证噪音测试分数是否显示百分比单位',
  '检查噪音测试等级是否正确显示（优秀/良好/需改进）',
  '使用过滤器分别查看听音辨图和噪音测试记录',
  '对比听音辨图和噪音测试的显示格式是否正确'
];

testSuggestions.forEach((suggestion, index) => {
  console.log(`${index + 1}. ${suggestion}`);
});

console.log('\n' + '='.repeat(50));
console.log('🎉 噪音测试分数显示验证完成！');

// 9. 预期结果
console.log('\n📊 预期结果:');
console.log('✅ 噪音测试记录显示准确率百分比');
console.log('✅ 听音辨图记录显示SAI分数');
console.log('✅ 不同测试类型使用不同的单位');
console.log('✅ 噪音测试显示对应的等级评价');
console.log('✅ 过滤功能正常工作');

console.log('\n🚀 现在噪音测试应该正确显示分数了！');
