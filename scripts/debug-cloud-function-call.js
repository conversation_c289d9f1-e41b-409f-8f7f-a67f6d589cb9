// 调试云函数调用问题
const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-0gjev5gfdef4d262'
})

async function debugCloudFunctionCall() {
  try {
    console.log('🔍 调试云函数调用问题...')
    
    // 1. 测试getResourceList云函数
    console.log('\n📋 测试getResourceList云函数...')
    const getResourceListResult = await cloud.callFunction({
      name: 'getResourceList',
      data: {}
    })
    
    console.log('getResourceList调用结果:', JSON.stringify(getResourceListResult.result, null, 2))
    
    if (getResourceListResult.result.success) {
      const { audio, image } = getResourceListResult.result.data
      console.log(`📊 资源统计: 音频${audio.length}个, 图片${image.length}个`)
      
      // 找到冰箱相关的文件
      const bingxiangFiles = [
        ...audio.filter(item => item.word === '冰箱'),
        ...image.filter(item => item.word === '冰箱')
      ]
      
      console.log('\n🧊 冰箱相关文件:')
      bingxiangFiles.forEach(file => {
        console.log(`- ${file.word} (${file.gender || 'image'}): ${file.cloudPath}`)
      })
      
      // 2. 测试downloadResource云函数
      console.log('\n🔧 测试downloadResource云函数...')
      for (const file of bingxiangFiles) {
        console.log(`\n测试文件: ${file.cloudPath}`)
        
        try {
          const downloadResult = await cloud.callFunction({
            name: 'downloadResource',
            data: {
              cloudPath: file.cloudPath,
              type: file.gender ? 'audio' : 'image'
            }
          })
          
          console.log('downloadResource结果:', JSON.stringify(downloadResult.result, null, 2))
          
          if (downloadResult.result.success) {
            console.log(`✅ 云函数成功: ${file.cloudPath}`)
            console.log(`临时链接: ${downloadResult.result.data.tempFileURL}`)
          } else {
            console.log(`❌ 云函数失败: ${file.cloudPath}`)
            console.log(`错误: ${downloadResult.result.error}`)
          }
        } catch (error) {
          console.log(`❌ 云函数调用异常: ${file.cloudPath}`)
          console.log(`异常: ${error.message}`)
        }
      }
      
      // 3. 测试直接调用getTempFileURL
      console.log('\n🔗 测试直接调用getTempFileURL...')
      for (const file of bingxiangFiles) {
        console.log(`\n测试文件: ${file.cloudPath}`)
        
        try {
          const tempFileResult = await cloud.getTempFileURL({
            fileList: [file.cloudPath]
          })
          
          const fileInfo = tempFileResult.fileList[0]
          if (fileInfo.tempFileURL) {
            console.log(`✅ 直接调用成功: ${file.cloudPath}`)
            console.log(`临时链接: ${fileInfo.tempFileURL}`)
          } else {
            console.log(`❌ 直接调用失败: ${file.cloudPath}`)
            console.log(`错误: ${fileInfo.errMsg}`)
          }
        } catch (error) {
          console.log(`❌ 直接调用异常: ${file.cloudPath}`)
          console.log(`异常: ${error.message}`)
        }
      }
      
    } else {
      console.log('❌ getResourceList失败:', getResourceListResult.result.error)
    }
    
  } catch (error) {
    console.error('❌ 调试失败:', error)
  }
}

// 运行调试
debugCloudFunctionCall()
