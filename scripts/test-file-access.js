// 测试文件访问问题
console.log('🧪 测试文件访问问题')

console.log('\n请在微信开发者工具控制台中执行以下代码:')

const testCode = `
console.log('🔍 开始测试文件访问...');

// 测试的文件列表
const testFiles = [
  'resources/images/wordlist/冰箱.png',
  'resources/images/wordlist/书本.png',
  'resources/audio/female/冰箱.mp3',
  'resources/audio/male/冰箱.mp3'
];

// 1. 测试直接调用 wx.cloud.getTempFileURL
console.log('\\n📋 测试直接调用 wx.cloud.getTempFileURL...');

testFiles.forEach((cloudPath, index) => {
  console.log(\`\\n\${index + 1}. 测试文件: \${cloudPath}\`);
  
  wx.cloud.getTempFileURL({
    fileList: [cloudPath],
    success: (res) => {
      console.log('完整响应:', res);
      
      if (res.fileList && res.fileList.length > 0) {
        const fileInfo = res.fileList[0];
        console.log('文件信息:', fileInfo);
        
        if (fileInfo.tempFileURL) {
          console.log(\`✅ 成功: \${cloudPath}\`);
          console.log(\`临时链接: \${fileInfo.tempFileURL}\`);
        } else {
          console.log(\`❌ 失败: \${cloudPath}\`);
          console.log(\`错误信息: \${fileInfo.errMsg}\`);
          console.log(\`错误代码: \${fileInfo.errCode}\`);
          console.log(\`状态: \${fileInfo.status}\`);
        }
      } else {
        console.log(\`❌ 响应为空: \${cloudPath}\`);
      }
    },
    fail: (err) => {
      console.log(\`❌ 调用失败: \${cloudPath}\`, err);
    }
  });
});

// 2. 测试云函数调用
console.log('\\n☁️ 测试云函数调用...');

// 先测试 getResourceList
wx.cloud.callFunction({
  name: 'getResourceList',
  data: {},
  success: (res) => {
    console.log('getResourceList结果:', res);
    
    if (res.result && res.result.success) {
      console.log('✅ getResourceList成功');
      const { audio, image } = res.result.data;
      console.log(\`资源统计: 音频\${audio.length}个, 图片\${image.length}个\`);
      
      // 找到冰箱相关的文件
      const bingxiangFiles = [
        ...audio.filter(item => item.word === '冰箱'),
        ...image.filter(item => item.word === '冰箱')
      ];
      
      console.log('冰箱相关文件:', bingxiangFiles);
      
      // 测试 downloadResource 云函数
      if (bingxiangFiles.length > 0) {
        const testFile = bingxiangFiles[0];
        console.log(\`\\n测试downloadResource: \${testFile.cloudPath}\`);
        
        wx.cloud.callFunction({
          name: 'downloadResource',
          data: {
            cloudPath: testFile.cloudPath,
            type: testFile.gender ? 'audio' : 'image'
          },
          success: (downloadRes) => {
            console.log('downloadResource结果:', downloadRes);
            
            if (downloadRes.result && downloadRes.result.success) {
              console.log('✅ downloadResource成功');
              console.log('临时链接:', downloadRes.result.data.tempFileURL);
            } else {
              console.log('❌ downloadResource失败');
              console.log('错误:', downloadRes.result?.error);
            }
          },
          fail: (err) => {
            console.log('❌ downloadResource调用失败:', err);
          }
        });
      }
    } else {
      console.log('❌ getResourceList失败:', res.result?.error);
    }
  },
  fail: (err) => {
    console.log('❌ getResourceList调用失败:', err);
  }
});

// 3. 测试环境信息
console.log('\\n🔧 检查环境信息...');
console.log('当前环境:', wx.cloud.getEnvironment ? wx.cloud.getEnvironment() : '无法获取');

// 4. 测试不同的路径格式
console.log('\\n🔍 测试不同路径格式...');

const pathVariants = [
  'resources/images/wordlist/冰箱.png',
  '/resources/images/wordlist/冰箱.png',
  'cloud://cloud1-0gjev5gfdef4d262.636c-cloud1-0gjev5gfdef4d262-1330046817/resources/images/wordlist/冰箱.png'
];

pathVariants.forEach((path, index) => {
  console.log(\`\\n路径变体 \${index + 1}: \${path}\`);
  
  wx.cloud.getTempFileURL({
    fileList: [path],
    success: (res) => {
      const fileInfo = res.fileList[0];
      console.log(\`结果: \${fileInfo.tempFileURL ? '✅成功' : '❌失败'}\`);
      if (!fileInfo.tempFileURL) {
        console.log(\`错误: \${fileInfo.errMsg}\`);
      }
    },
    fail: (err) => {
      console.log('调用失败:', err);
    }
  });
});
`

console.log(testCode)

console.log('\n📝 执行步骤:')
console.log('1. 在微信开发者工具中打开项目')
console.log('2. 打开控制台（Console）')
console.log('3. 复制粘贴上面的代码')
console.log('4. 按回车执行')
console.log('5. 观察输出结果')

console.log('\n🎯 关键观察点:')
console.log('1. 直接调用是否成功？')
console.log('2. 云函数调用是否成功？')
console.log('3. 不同路径格式哪个有效？')
console.log('4. 错误信息的具体内容')

console.log('\n请执行测试并告诉我结果！')
