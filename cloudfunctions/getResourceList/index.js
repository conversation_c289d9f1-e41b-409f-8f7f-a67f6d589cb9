const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-0gjev5gfdef4d262'
})

const resources = require('./resources')

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { type } = event // 'audio' 或 'image'
    
    console.log('获取资源列表:', { type, openid: wxContext.OPENID })
    
    // 环境信息 - 使用正确的 fileID 格式
    const envId = 'cloud1-0gjev5gfdef4d262'
    // 使用你提供的正确存储桶ID
    const bucketId = '636c-cloud1-0gjev5gfdef4d262-1314700961'

    let audioList = []
    let imageList = []

    if (!type || type === 'audio') {
      // 女声音频
      resources.audio.female.forEach(word => {
        audioList.push({
          word: word,
          gender: 'female',
          path: `audio/女声/${word}.mp3`,
          cloudPath: `cloud://${envId}.${bucketId}/resources/audio/female/${word}.mp3`
        })
      })
      
      // 男声音频
      resources.audio.male.forEach(word => {
        audioList.push({
          word: word,
          gender: 'male',
          path: `audio/男声/${word}.mp3`,
          cloudPath: `cloud://${envId}.${bucketId}/resources/audio/male/${word}.mp3`
        })
      })
    }

    if (!type || type === 'image') {
      // 词汇图片（只有wordlist图片实际存在）
      resources.image.wordlist.forEach(word => {
        imageList.push({
          word: word,
          category: 'wordlist',
          path: `images/wordlist/${word}.png`,
          cloudPath: `cloud://${envId}.${bucketId}/resources/images/wordlist/${word}.png`
        })
      })
    }

    // 如果指定了类型，只返回对应类型的资源
    if (type === 'audio') {
      return {
        success: true,
        data: {
          audio: audioList,
          summary: {
            audio: {
              female: resources.audio.female.length,
              male: resources.audio.male.length,
              total: audioList.length
            },
            total: audioList.length
          }
        }
      }
    } else if (type === 'image') {
      return {
        success: true,
        data: {
          image: imageList,
          summary: {
            image: {
              wordlist: resources.image.wordlist.length,
              total: imageList.length
            },
            total: imageList.length
          }
        }
      }
    } else {
      // 返回所有资源
      return {
        success: true,
        data: {
          audio: audioList,
          image: imageList,
          summary: {
            audio: {
              female: resources.audio.female.length,
              male: resources.audio.male.length,
              total: audioList.length
            },
            image: {
              wordlist: resources.image.wordlist.length,
              total: imageList.length
            },
            total: audioList.length + imageList.length
          }
        }
      }
    }

  } catch (error) {
    console.error('获取资源列表失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
