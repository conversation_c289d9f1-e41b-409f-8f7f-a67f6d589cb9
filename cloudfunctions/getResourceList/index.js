const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-0gjev5gfdef4d262'
})

const db = cloud.database()

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()

  try {
    const { type } = event // 'audio' 或 'image'

    console.log('获取资源列表:', { type, openid: wxContext.OPENID })

    // 环境信息 - 使用完整的 fileID 格式
    const envId = 'cloud1-0gjev5gfdef4d262'
    // 根据腾讯云开发文档示例格式推断正确的存储桶ID
    const bucketId = '7765-cloud1-0gjev5gfdef4d262-1251059088'
    
    // 定义实际存在的资源列表（与上传的文件一致）
    const resources = {
      audio: {
        female: [
          '书本', '冰箱', '回家', '大象', '太阳', '山脉', '帽子', '报纸', '时间', '月亮',
          '桌子', '气球', '汽车', '河流', '熊猫', '电视', '电话', '窗户', '自行车', '花朵',
          '苹果', '蛋糕', '衣服', '铅笔', '鞋子'
        ],
        male: [
          '书本', '冰箱', '回家', '大象', '太阳', '山脉', '帽子', '报纸', '时间', '月亮',
          '桌子', '气球', '汽车', '河流', '熊猫', '电视', '电话', '窗户', '自行车', '花朵',
          '苹果', '蛋糕', '衣服', '铅笔', '鞋子'
        ]
      },
      image: {
        wordlist: [
          '书本', '冰箱', '回家', '大象', '太阳', '山脉', '帽子', '报纸', '时间', '月亮',
          '桌子', '气球', '汽车', '河流', '熊猫', '电视', '电话', '窗户', '自行车', '花朵',
          '苹果', '蛋糕', '衣服', '铅笔', '鞋子'
        ]
        // 移除words数组，因为实际只上传了wordlist图片
      }
    }
    
    if (type === 'audio') {
      // 返回音频资源列表
      const audioList = []
      
      // 女声音频
      resources.audio.female.forEach(word => {
        audioList.push({
          word: word,
          gender: 'female',
          path: `audio/女声/${word}.mp3`,
          cloudPath: `cloud://${envId}.${bucketId}/resources/audio/female/${word}.mp3`
        })
      })

      // 男声音频
      resources.audio.male.forEach(word => {
        audioList.push({
          word: word,
          gender: 'male',
          path: `audio/男声/${word}.mp3`,
          cloudPath: `cloud://${envId}.${bucketId}/resources/audio/male/${word}.mp3`
        })
      })
      
      return {
        success: true,
        data: audioList,
        total: audioList.length
      }
      
    } else if (type === 'image') {
      // 返回图片资源列表
      const imageList = []
      
      // 词汇图片（只有wordlist图片实际存在）
      resources.image.wordlist.forEach(word => {
        imageList.push({
          word: word,
          category: 'wordlist',
          path: `images/wordlist/${word}.png`,
          cloudPath: `cloud://${envId}.${bucketId}/resources/images/wordlist/${word}.png`
        })
      })
      
      return {
        success: true,
        data: imageList,
        total: imageList.length
      }
      
    } else {
      // 返回所有资源列表
      const audioList = []
      const imageList = []

      // 生成音频资源列表
      resources.audio.female.forEach(word => {
        audioList.push({
          word: word,
          gender: 'female',
          path: `audio/女声/${word}.mp3`,
          cloudPath: `cloud://${envId}.${bucketId}/resources/audio/female/${word}.mp3`
        })
      })

      resources.audio.male.forEach(word => {
        audioList.push({
          word: word,
          gender: 'male',
          path: `audio/男声/${word}.mp3`,
          cloudPath: `cloud://${envId}.${bucketId}/resources/audio/male/${word}.mp3`
        })
      })

      // 生成图片资源列表
      resources.image.wordlist.forEach(word => {
        imageList.push({
          word: word,
          category: 'wordlist',
          path: `images/wordlist/${word}.png`,
          cloudPath: `cloud://${envId}.${bucketId}/resources/images/wordlist/${word}.png`
        })
      })

      return {
        success: true,
        data: {
          audio: audioList,
          image: imageList,
          summary: {
            audio: {
              female: resources.audio.female.length,
              male: resources.audio.male.length,
              total: audioList.length
            },
            image: {
              wordlist: resources.image.wordlist.length,
              total: imageList.length
            },
            total: audioList.length + imageList.length
          }
        }
      }
    }
    
  } catch (error) {
    console.error('获取资源列表失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
