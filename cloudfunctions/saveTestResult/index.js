const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { 
      testResults, 
      saiScore, 
      saiLevel, 
      saiLevelClass,
      testDate, 
      testType 
    } = event
    
    console.log('保存测试结果:', {
      openid: wxContext.OPENID,
      saiScore,
      saiLevel,
      testType
    })
    
    // 构建要保存的数据
    const testRecord = {
      openid: wxContext.OPENID,
      testResults: testResults,
      saiScore: saiScore,
      saiLevel: saiLevel,
      saiLevelClass: saiLevelClass,
      testDate: testDate || new Date(),
      testType: testType || 'hearing_sai',
      createTime: new Date(),
      updateTime: new Date()
    }
    
    // 保存到数据库
    const result = await db.collection('hearing_test_results').add({
      data: testRecord
    })
    
    console.log('测试结果保存成功:', result)
    
    return {
      success: true,
      recordId: result._id,
      message: '测试结果保存成功'
    }
    
  } catch (error) {
    console.error('保存测试结果失败:', error)
    return {
      success: false,
      error: error.message,
      message: '保存测试结果失败'
    }
  }
}
