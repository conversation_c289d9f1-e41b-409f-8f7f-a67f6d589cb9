const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-0gjev5gfdef4d262'
})

exports.main = async (event, context) => {
  try {
    console.log('测试云存储访问')
    
    // 尝试列出文件
    console.log('尝试列出文件...')
    const listResult = await cloud.storage().listFile({
      prefix: 'resources/images/wordlist/',
      maxKeys: 5
    })
    
    console.log('列出文件结果:', JSON.stringify(listResult, null, 2))
    
    if (listResult.fileList && listResult.fileList.length > 0) {
      const firstFile = listResult.fileList[0]
      console.log('第一个文件:', firstFile)
      
      // 尝试获取这个文件的临时链接
      console.log('尝试获取临时链接:', firstFile.fileID)
      
      const tempResult = await cloud.getTempFileURL({
        fileList: [firstFile.fileID]
      })
      
      console.log('临时链接结果:', JSON.stringify(tempResult, null, 2))
      
      return {
        success: true,
        data: {
          listResult,
          tempResult,
          firstFileID: firstFile.fileID
        }
      }
    } else {
      return {
        success: false,
        error: '没有找到文件'
      }
    }
    
  } catch (error) {
    console.error('测试失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
