const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-0gjev5gfdef4d262'
})

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { cloudPath, type } = event // cloudPath: 云存储路径, type: 'audio' 或 'image'
    
    console.log('下载资源:', { cloudPath, type, openid: wxContext.OPENID })
    
    // 获取云存储文件的临时链接
    console.log('尝试获取临时链接:', cloudPath)

    // 尝试不同的路径格式
    const pathVariants = [
      cloudPath, // 原始路径
      `cloud://cloud1-0gjev5gfdef4d262.636c-cloud1-0gjev5gfdef4d262-1330046817/${cloudPath}`, // 完整fileID格式
      cloudPath.startsWith('/') ? cloudPath : `/${cloudPath}`, // 添加前缀斜杠
      cloudPath.startsWith('resources/') ? cloudPath.substring(10) : cloudPath // 移除resources前缀
    ]

    let result = null
    let successPath = null

    for (const testPath of pathVariants) {
      console.log(`测试路径: ${testPath}`)
      try {
        result = await cloud.getTempFileURL({
          fileList: [testPath]
        })

        console.log(`路径 ${testPath} 结果:`, JSON.stringify(result, null, 2))

        if (result.fileList && result.fileList.length > 0 && result.fileList[0].status === 0) {
          successPath = testPath
          console.log(`✅ 成功路径: ${testPath}`)
          break
        }
      } catch (error) {
        console.log(`路径 ${testPath} 失败:`, error.message)
      }
    }

    if (!successPath) {
      console.log('所有路径格式都失败了')
    }

    if (result.fileList && result.fileList.length > 0) {
      const fileInfo = result.fileList[0]

      if (fileInfo.status === 0) {
        // 成功获取临时链接
        return {
          success: true,
          data: {
            tempFileURL: fileInfo.tempFileURL,
            cloudPath: cloudPath,
            type: type,
            maxAge: fileInfo.maxAge || 7200 // 默认2小时有效期
          }
        }
      } else {
        console.error('获取临时链接失败:', fileInfo)
        return {
          success: false,
          error: `获取文件临时链接失败: ${fileInfo.errMsg}`
        }
      }
    } else {
      return {
        success: false,
        error: '未找到文件'
      }
    }
    
  } catch (error) {
    console.error('下载资源失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
