const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { cloudPath, type } = event // cloudPath: 云存储路径, type: 'audio' 或 'image'
    
    console.log('下载资源:', { cloudPath, type, openid: wxContext.OPENID })
    
    // 获取云存储文件的临时链接
    console.log('尝试获取临时链接:', cloudPath)
    const result = await cloud.getTempFileURL({
      fileList: [cloudPath]
    })

    console.log('getTempFileURL结果:', JSON.stringify(result, null, 2))

    if (result.fileList && result.fileList.length > 0) {
      const fileInfo = result.fileList[0]

      if (fileInfo.status === 0) {
        // 成功获取临时链接
        return {
          success: true,
          data: {
            tempFileURL: fileInfo.tempFileURL,
            cloudPath: cloudPath,
            type: type,
            maxAge: fileInfo.maxAge || 7200 // 默认2小时有效期
          }
        }
      } else {
        console.error('获取临时链接失败:', fileInfo)
        return {
          success: false,
          error: `获取文件临时链接失败: ${fileInfo.errMsg}`
        }
      }
    } else {
      return {
        success: false,
        error: '未找到文件'
      }
    }
    
  } catch (error) {
    console.error('下载资源失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
