// 腾讯云TTS语音合成云函数
// 开发工具：用于生成本地音频文件，不在用户测试流程中使用
// 注意：此功能与用户听力测试功能完全脱钩，仅供开发阶段使用

const cloud = require('wx-server-sdk')
const https = require('https')
const crypto = require('crypto')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 腾讯云TTS配置
const TTS_CONFIG = {
  secretId: process.env.TENCENT_SECRET_ID || 'your-secret-id',
  secretKey: process.env.TENCENT_SECRET_KEY || 'your-secret-key',
  region: 'ap-beijing',
  endpoint: 'tts.tencentcloudapi.com',
  service: 'tts',
  version: '2019-08-23',
  action: 'TextToVoice'
}

// 生成腾讯云API签名
function generateSignature(params, secretKey) {
  const sortedParams = Object.keys(params).sort().map(key => `${key}=${params[key]}`).join('&')
  const stringToSign = `POST\n${TTS_CONFIG.endpoint}\n/\n${sortedParams}`
  return crypto.createHmac('sha1', secretKey).update(stringToSign).digest('base64')
}

// 调用腾讯云TTS API
async function callTencentTTS(text, voiceType = 0, speed = 0, volume = 0) {
  return new Promise((resolve, reject) => {
    const timestamp = Math.floor(Date.now() / 1000)
    const nonce = Math.floor(Math.random() * 1000000)

    const params = {
      Action: TTS_CONFIG.action,
      Version: TTS_CONFIG.version,
      Region: TTS_CONFIG.region,
      Text: text,
      SessionId: `session_${timestamp}_${nonce}`,
      VoiceType: voiceType, // 0: 女声，1: 男声
      Speed: speed, // 语速 -2到2
      Volume: volume, // 音量 -10到10
      SampleRate: 16000,
      Codec: 'mp3',
      Timestamp: timestamp,
      Nonce: nonce,
      SecretId: TTS_CONFIG.secretId
    }

    const signature = generateSignature(params, TTS_CONFIG.secretKey)
    params.Signature = signature

    const postData = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')

    const options = {
      hostname: TTS_CONFIG.endpoint,
      port: 443,
      path: '/',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': Buffer.byteLength(postData)
      }
    }

    const req = https.request(options, (res) => {
      let data = ''
      res.on('data', (chunk) => {
        data += chunk
      })
      res.on('end', () => {
        try {
          const result = JSON.parse(data)
          if (result.Response.Error) {
            reject(new Error(result.Response.Error.Message))
          } else {
            resolve(result.Response.Audio)
          }
        } catch (error) {
          reject(error)
        }
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    req.write(postData)
    req.end()
  })
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()

  try {
    const { text, soundLevel, useRealTTS = false } = event

    console.log('🔧 开发工具 - TTS请求参数:', { text, soundLevel, useRealTTS })
    console.log('⚠️ 注意：此功能仅用于开发阶段生成音频文件，不在用户测试流程中使用')

    if (useRealTTS && TTS_CONFIG.secretId !== 'your-secret-id') {
      // 使用真实的腾讯云TTS服务
      try {
        const audioBase64 = await callTencentTTS(text)

        // 将音频数据上传到云存储
        const fileName = `tts/${text}_${soundLevel}db_${Date.now()}.mp3`
        const audioBuffer = Buffer.from(audioBase64, 'base64')

        const uploadResult = await cloud.uploadFile({
          cloudPath: fileName,
          fileContent: audioBuffer
        })

        return {
          success: true,
          audioUrl: uploadResult.fileID,
          text: text,
          soundLevel: soundLevel,
          message: 'TTS生成成功'
        }
      } catch (ttsError) {
        console.error('腾讯云TTS调用失败:', ttsError)
        // 降级到模拟模式
      }
    }

    // 模拟TTS处理延时
    await new Promise(resolve => setTimeout(resolve, 500))

    // 模拟生成的音频URL - 使用云存储路径格式
    const audioUrl = `cloud://hearing-test.6865-hearing-test-1g8qqhzs0a7e7c7e/audio/tts/${encodeURIComponent(text)}_${soundLevel}db.mp3`

    return {
      success: true,
      audioUrl: audioUrl,
      text: text,
      soundLevel: soundLevel,
      message: 'TTS生成成功（模拟模式）'
    }

  } catch (error) {
    console.error('TTS生成失败:', error)
    return {
      success: false,
      error: error.message,
      message: 'TTS生成失败'
    }
  }
}
