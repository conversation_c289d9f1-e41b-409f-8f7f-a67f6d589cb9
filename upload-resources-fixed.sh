#!/bin/bash
# 修复后的资源上传脚本
# 使用正确的源文件路径

echo "🚀 开始批量上传资源到云存储..."
echo "使用正确的源文件路径: source/"
echo ""

# 设置错误处理
set -e

# 检查源文件是否存在
echo "🔍 检查源文件..."
if [ ! -d "source/audio/女声" ]; then
  echo "❌ 源目录不存在: source/audio/女声"
  exit 1
fi

if [ ! -d "source/audio/男声" ]; then
  echo "❌ 源目录不存在: source/audio/男声"
  exit 1
fi

echo "✅ 源目录检查通过"
echo ""

# 计数器
count=0
total=75

# 定义文件列表
words=("书本" "冰箱" "回家" "大象" "太阳" "山脉" "帽子" "报纸" "时间" "月亮" "桌子" "气球" "汽车" "河流" "熊猫" "电视" "电话" "窗户" "自行车" "花朵" "苹果" "蛋糕" "衣服" "铅笔" "鞋子")

echo "📤 开始上传女声音频文件..."
for word in "${words[@]}"; do
  echo "📤 上传进度: $((++count))/$total - 女声: $word"
  
  source_file="source/audio/女声/${word}.mp3"
  target_path="resources/audio/female/${word}.mp3"
  
  if [ ! -f "$source_file" ]; then
    echo "⚠️ 源文件不存在: $source_file"
    continue
  fi
  
  tcb storage upload "$source_file" "$target_path"
  if [ $? -eq 0 ]; then
    echo "✅ 上传成功: $word (女声)"
  else
    echo "❌ 上传失败: $word (女声)"
    exit 1
  fi
  echo ""
done

echo "📤 开始上传男声音频文件..."
for word in "${words[@]}"; do
  echo "📤 上传进度: $((++count))/$total - 男声: $word"
  
  source_file="source/audio/男声/${word}.mp3"
  target_path="resources/audio/male/${word}.mp3"
  
  if [ ! -f "$source_file" ]; then
    echo "⚠️ 源文件不存在: $source_file"
    continue
  fi
  
  tcb storage upload "$source_file" "$target_path"
  if [ $? -eq 0 ]; then
    echo "✅ 上传成功: $word (男声)"
  else
    echo "❌ 上传失败: $word (男声)"
    exit 1
  fi
  echo ""
done

echo "📤 开始上传图片文件..."
# 检查图片源目录
image_source_dir=""
if [ -d "source/images/wordlist" ]; then
  image_source_dir="source/images/wordlist"
elif [ -d "source/images/wordlist_compressed" ]; then
  image_source_dir="source/images/wordlist_compressed"
elif [ -d "miniprogram/images/wordlist_compressed" ]; then
  image_source_dir="miniprogram/images/wordlist_compressed"
else
  echo "⚠️ 未找到图片源目录，跳过图片上传"
  image_source_dir=""
fi

if [ -n "$image_source_dir" ]; then
  echo "📁 使用图片源目录: $image_source_dir"
  
  for word in "${words[@]}"; do
    echo "📤 上传进度: $((++count))/$total - 图片: $word"
    
    source_file="${image_source_dir}/${word}.png"
    target_path="resources/images/wordlist/${word}.png"
    
    if [ ! -f "$source_file" ]; then
      echo "⚠️ 源文件不存在: $source_file"
      continue
    fi
    
    tcb storage upload "$source_file" "$target_path"
    if [ $? -eq 0 ]; then
      echo "✅ 上传成功: $word (图片)"
    else
      echo "❌ 上传失败: $word (图片)"
      exit 1
    fi
    echo ""
  done
else
  echo "⚠️ 跳过图片上传，因为未找到源目录"
fi

echo "🎉 资源上传完成！"
echo "📊 处理了 $count 个文件"
echo ""
echo "🔍 验证上传结果..."
echo "请在微信开发者工具中执行以下代码验证："
echo ""
echo "wx.cloud.getTempFileURL({"
echo "  fileList: ['resources/audio/male/窗户.mp3'],"
echo "  success: (res) => {"
echo "    console.log('验证结果:', res.fileList[0].tempFileURL ? '✅文件存在' : '❌文件不存在');"
echo "  }"
echo "});"
