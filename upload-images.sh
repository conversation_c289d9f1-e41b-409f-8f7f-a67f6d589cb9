#!/bin/bash
# 上传图片文件

echo "🖼️ 开始上传图片文件..."

# 设置错误处理
set -e

# 定义文件列表
words=("书本" "冰箱" "回家" "大象" "太阳" "山脉" "帽子" "报纸" "时间" "月亮" "桌子" "气球" "汽车" "河流" "熊猫" "电视" "电话" "窗户" "自行车" "花朵" "苹果" "蛋糕" "衣服" "铅笔" "鞋子")

count=0
total=25

for word in "${words[@]}"; do
  echo "📤 上传进度: $((++count))/$total - 图片: $word"
  
  source_file="source/wordlist/${word}.png"
  target_path="resources/images/wordlist/${word}.png"
  
  if [ ! -f "$source_file" ]; then
    echo "⚠️ 源文件不存在: $source_file"
    continue
  fi
  
  tcb storage upload "$source_file" "$target_path"
  if [ $? -eq 0 ]; then
    echo "✅ 上传成功: $word (图片)"
  else
    echo "❌ 上传失败: $word (图片)"
    exit 1
  fi
  echo ""
done

echo "🎉 图片上传完成！"
echo "📊 处理了 $count 个图片文件"
