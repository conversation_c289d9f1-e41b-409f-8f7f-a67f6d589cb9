.restaurant-list {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  background-color: #f5f5f5;
  max-height: 900px;
  border-radius: 8px;
}

.restaurant-content {
  flex: 1;
  overflow-y: scroll;
}

.restaurant-box {
  /* display: flex;
  flex-direction: row; */
  background-color: #fff;
  border-radius: 16rpx;
  /* box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1); */
  margin-bottom: 20rpx;
  overflow: hidden;
}

.restaurant-card {
  display: flex;
  flex-direction: row;
  /* background-color: #fff; */
  /* border-radius: 16rpx; */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  /* margin-bottom: 20rpx; */
  /* overflow: hidden; */
}

.restaurant-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 16rpx 0 0 16rpx;
}

.restaurant-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.restaurant-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.restaurant-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  width: 65%;
}

.rating {
  display: flex;
  align-items: left;
  flex-direction: column;
}

.score {
  font-size: 28rpx;
  color: #ff9900;
  font-weight: bold;
}

.reviews {
  font-size: 24rpx;
  color: #999;
  /* margin-left: 10rpx; */
  margin-right: 10rpx;
}

.description {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.contact-info {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.phone, .address {
  display: block;
}

.call-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
  color: #fff;
  font-size: 28rpx;
  border-radius: 14rpx;
  padding: 14rpx 20rpx;
}

.call-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}