{"miniprogramRoot": "miniprogram/", "cloudfunctionRoot": "cloudfunctions/", "setting": {"urlCheck": true, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "useMultiFrameRuntime": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "ignoreUploadUnusedFiles": true, "compileWorklet": false, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "cloudfunctionTemplateRoot": "cloudfunctionTemplate/", "condition": {"search": {"list": []}, "conversation": {"list": []}, "plugin": {"list": []}, "game": {"list": []}, "miniprogram": {"list": []}}, "compileType": "miniprogram", "srcMiniprogramRoot": "miniprogram/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "packOptions": {"ignore": [], "include": []}, "appid": "wx5ceb4e4809aa1d28", "libVersion": "3.7.10", "simulatorPluginLibVersion": {}}