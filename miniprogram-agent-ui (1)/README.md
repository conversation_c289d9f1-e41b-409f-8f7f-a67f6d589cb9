agent集成流程：

初始化
微信小程序基础库从 3.7.1 版本开始内置了云开发 AI+ 能力，开发者可以直接通过小程序中的 wx.cloud.extend.AI 调用。在使用基础库 AI+ 能力前，需要传入云开发环境进行初始化。


wx.cloud.init({
env: "cloud1-0gjev5gfdef4d262"
});
初始化完毕后，即可通过 wx.cloud.extend.AI 使用云开发 AI+ 能力。

获取 Agent 信息

const res = await wx.cloud.extend.AI.bot.get({ botId: "bot-f823faa5" });
console.log(res);
详细用法可参考:
发送消息

const res = await wx.cloud.extend.AI.bot.sendMessage({
data: {
botId: 'bot-f823faa5',
msg: "你是谁"
}
})

for await (let event of res.eventStream) {
// 收到结束信号，终止循环
if (event.data === '[DONE]') {
break;
}

    const data = JSON.parse(event.data);

    // 打印思维链内容
    // 若使用模型为 deepseek-r1，则输出中包含思维链
    const think = data.reasoning_content;
    if (think) {
      console.log(think);
    }

    // 打印输出正文
    const content = data.content;
    if (content) {
      console.log(content);
    }
}

// 无需解析其他字段时，也可以直接打印输出正文
// for await (let text of res.textStream) {
//     console.log(text);
// }
详细用法可参考:
查看与 Agent 的聊天记录

const res = await wx.cloud.extend.AI.bot.getChatRecords({
botId: "bot-f823faa5",
pageNumber: 1,
pageSize: 10,
sort: "asc",
});
console.log(res);
详细用法可参考:
对某一条聊天记录进行反馈

const res = await wx.cloud.extend.AI.bot.sendFeedback({
userFeedback: {
botId: "bot-f823faa5",
recordId: "recordId-xxx",
comment: "非常棒",
rating: 5,
tags: ["优美"],
aiAnswer: "落英缤纷",
input: "来个成语",
type: "upvote",
},
});
console.log(res);
详细用法可参考:
查看反馈记录

const res = await wx.cloud.extend.AI.bot.getFeedBack({
botId: "bot-f823faa5",
from: 0,
to: 0,
maxRating: 4,
minRating: 3,
pageNumber: 1,
pageSize: 10,
sender: "user-a",
senderFilter: "include",
type: "upvote",
});
console.log(res)
详细用法可参考:
获取 Agent 推荐问题

const res = await wx.cloud.extend.AI.bot.getRecommendQuestions({
data: {
botId: "bot-f823faa5",
msg: "你是谁"
}
})
for await (let x of res.textStream) {
console.log(x)
}

细节参考URL：https://docs.cloudbase.net/ai/sdk-reference/wxExtendAi#aibotgetrecommendquestions