# TTS开发工具使用说明

## 📋 概述

TTS（Text-to-Speech）云函数是一个**开发阶段工具**，用于生成本地音频文件。此功能与用户听力测试功能**完全脱钩**，仅供开发人员使用。

## ⚠️ 重要说明

- **用户测试流程**：只使用本地预录制的音频文件（`/audio/*.mp3`）
- **TTS功能**：仅作为开发工具，用于生成新的音频文件
- **完全脱钩**：用户在测试过程中不会触发TTS生成

## 🔧 使用场景

1. **添加新词汇**：当需要添加新的测试词汇时
2. **批量生成**：为大量词汇生成音频文件
3. **音质统一**：确保所有音频文件的音质和音量一致

## 🚀 使用方法

### 方法1：通过小程序开发者工具调用

```javascript
// 在开发者工具的控制台中执行
wx.cloud.callFunction({
  name: 'generateTTS',
  data: {
    text: '测试词汇',        // 要生成的词汇
    soundLevel: 85,         // 音量级别（建议85dB作为基准）
    useRealTTS: true       // 使用真实TTS服务
  }
}).then(res => {
  console.log('TTS生成结果:', res);
  // res.result.audioUrl 包含生成的音频文件URL
});
```

### 方法2：批量生成脚本

```javascript
// 批量生成多个词汇的音频文件
const words = ['飞机', '火车', '电脑', '手机'];

words.forEach(async (word) => {
  try {
    const result = await wx.cloud.callFunction({
      name: 'generateTTS',
      data: {
        text: word,
        soundLevel: 85,
        useRealTTS: true
      }
    });
    console.log(`${word} 生成成功:`, result.result.audioUrl);
  } catch (error) {
    console.error(`${word} 生成失败:`, error);
  }
});
```

## ⚙️ 配置说明

### TTS参数配置

- **text**: 要转换的文字内容
- **soundLevel**: 音量级别（建议85dB）
- **useRealTTS**: 是否使用真实TTS服务
- **voiceType**: 声音类型（0=女声，1=男声）

### 腾讯云配置

需要在云函数环境变量中配置：

```bash
TENCENT_SECRET_ID=your-secret-id
TENCENT_SECRET_KEY=your-secret-key
```

## 📁 文件管理

### 生成的文件位置

TTS生成的音频文件会保存到云存储的 `tts/` 目录：

```
tts/
├── 飞机_85db_1234567890.mp3
├── 火车_85db_1234567891.mp3
└── ...
```

### 下载到本地

1. 在云开发控制台的存储管理中找到生成的文件
2. 下载文件到本地 `miniprogram/audio/` 目录
3. 重命名为标准格式：`词汇.mp3`

## 🎯 最佳实践

### 1. 音量标准化

- 所有音频文件以**85dB为基准**
- 通过代码中的音量系数调节到55dB、70dB
- 确保音量一致性

### 2. 文件命名规范

```
miniprogram/audio/
├── 飞机.mp3
├── 火车.mp3
├── 电脑.mp3
└── 手机.mp3
```

### 3. 质量检查

生成音频文件后，建议：
- 检查音频清晰度
- 验证发音准确性
- 测试音量一致性

## 🔍 故障排除

### 常见问题

1. **TTS服务调用失败**
   - 检查腾讯云API密钥配置
   - 确认账户余额充足

2. **音频质量问题**
   - 调整TTS参数（语速、音量）
   - 尝试不同的声音类型

3. **文件上传失败**
   - 检查云存储权限
   - 确认网络连接稳定

## 📝 开发流程

1. **需求确认**：确定需要生成的词汇列表
2. **批量生成**：使用TTS工具生成音频文件
3. **质量检查**：验证音频质量和准确性
4. **文件整理**：下载并重命名文件到正确位置
5. **配置更新**：更新word-mapping.js配置文件
6. **功能测试**：在听力测试中验证新音频文件

## 🚫 注意事项

- **不要在生产环境中依赖TTS**：用户测试只使用本地音频文件
- **成本控制**：TTS调用会产生费用，仅在必要时使用
- **版权合规**：确保生成的音频内容符合版权要求
