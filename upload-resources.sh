#!/bin/bash
# 批量上传资源到云存储
# 使用前请确保已安装并配置 @cloudbase/cli

echo "🚀 开始批量上传资源到云存储..."
echo "总共需要上传 75 个文件"
echo ""

# 设置错误处理
set -e

# 计数器
count=0
total=75


# 上传第 1 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/书本.mp3 resources/audio/female/书本.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 2 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/冰箱.mp3 resources/audio/female/冰箱.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 3 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/回家.mp3 resources/audio/female/回家.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 4 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/大象.mp3 resources/audio/female/大象.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 5 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/太阳.mp3 resources/audio/female/太阳.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 6 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/山脉.mp3 resources/audio/female/山脉.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 7 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/帽子.mp3 resources/audio/female/帽子.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 8 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/报纸.mp3 resources/audio/female/报纸.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 9 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/时间.mp3 resources/audio/female/时间.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 10 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/月亮.mp3 resources/audio/female/月亮.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 11 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/桌子.mp3 resources/audio/female/桌子.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 12 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/气球.mp3 resources/audio/female/气球.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 13 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/汽车.mp3 resources/audio/female/汽车.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 14 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/河流.mp3 resources/audio/female/河流.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 15 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/熊猫.mp3 resources/audio/female/熊猫.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 16 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/电视.mp3 resources/audio/female/电视.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 17 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/电话.mp3 resources/audio/female/电话.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 18 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/窗户.mp3 resources/audio/female/窗户.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 19 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/自行车.mp3 resources/audio/female/自行车.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 20 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/花朵.mp3 resources/audio/female/花朵.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 21 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/苹果.mp3 resources/audio/female/苹果.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 22 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/蛋糕.mp3 resources/audio/female/蛋糕.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 23 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/衣服.mp3 resources/audio/female/衣服.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 24 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/铅笔.mp3 resources/audio/female/铅笔.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 25 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/女声/鞋子.mp3 resources/audio/female/鞋子.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 26 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/书本.mp3 resources/audio/male/书本.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 27 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/冰箱.mp3 resources/audio/male/冰箱.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 28 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/回家.mp3 resources/audio/male/回家.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 29 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/大象.mp3 resources/audio/male/大象.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 30 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/太阳.mp3 resources/audio/male/太阳.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 31 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/山脉.mp3 resources/audio/male/山脉.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 32 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/帽子.mp3 resources/audio/male/帽子.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 33 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/报纸.mp3 resources/audio/male/报纸.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 34 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/时间.mp3 resources/audio/male/时间.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 35 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/月亮.mp3 resources/audio/male/月亮.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 36 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/桌子.mp3 resources/audio/male/桌子.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 37 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/气球.mp3 resources/audio/male/气球.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 38 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/汽车.mp3 resources/audio/male/汽车.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 39 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/河流.mp3 resources/audio/male/河流.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 40 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/熊猫.mp3 resources/audio/male/熊猫.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 41 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/电视.mp3 resources/audio/male/电视.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 42 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/电话.mp3 resources/audio/male/电话.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 43 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/窗户.mp3 resources/audio/male/窗户.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 44 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/自行车.mp3 resources/audio/male/自行车.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 45 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/花朵.mp3 resources/audio/male/花朵.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 46 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/苹果.mp3 resources/audio/male/苹果.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 47 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/蛋糕.mp3 resources/audio/male/蛋糕.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 48 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/衣服.mp3 resources/audio/male/衣服.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 49 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/铅笔.mp3 resources/audio/male/铅笔.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 50 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/audio/男声/鞋子.mp3 resources/audio/male/鞋子.mp3
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 51 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/书本.png resources/images/wordlist/书本.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 52 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/冰箱.png resources/images/wordlist/冰箱.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 53 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/回家.png resources/images/wordlist/回家.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 54 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/大象.png resources/images/wordlist/大象.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 55 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/太阳.png resources/images/wordlist/太阳.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 56 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/山脉.png resources/images/wordlist/山脉.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 57 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/帽子.png resources/images/wordlist/帽子.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 58 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/报纸.png resources/images/wordlist/报纸.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 59 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/时间.png resources/images/wordlist/时间.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 60 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/月亮.png resources/images/wordlist/月亮.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 61 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/桌子.png resources/images/wordlist/桌子.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 62 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/气球.png resources/images/wordlist/气球.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 63 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/汽车.png resources/images/wordlist/汽车.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 64 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/河流.png resources/images/wordlist/河流.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 65 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/熊猫.png resources/images/wordlist/熊猫.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 66 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/电视.png resources/images/wordlist/电视.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 67 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/电话.png resources/images/wordlist/电话.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 68 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/窗户.png resources/images/wordlist/窗户.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 69 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/自行车.png resources/images/wordlist/自行车.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 70 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/花朵.png resources/images/wordlist/花朵.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 71 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/苹果.png resources/images/wordlist/苹果.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 72 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/蛋糕.png resources/images/wordlist/蛋糕.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 73 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/衣服.png resources/images/wordlist/衣服.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 74 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/铅笔.png resources/images/wordlist/铅笔.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""

# 上传第 75 个文件
echo "📤 上传进度: $((++count))/$total"
tcb storage upload miniprogram/images/wordlist_compressed/鞋子.png resources/images/wordlist/鞋子.png
if [ $? -eq 0 ]; then
  echo "✅ 上传成功"
else
  echo "❌ 上传失败"
  exit 1
fi
echo ""


echo "🎉 所有资源上传完成！"
echo "📊 成功上传 $count 个文件"
